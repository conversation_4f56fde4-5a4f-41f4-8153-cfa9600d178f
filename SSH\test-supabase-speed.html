<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase连接速度测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .result {
            font-weight: bold;
            margin-top: 10px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Supabase连接速度测试工具</h1>
        <p>此工具帮助您测试不同配置下的Supabase连接速度</p>
        
        <div class="test-item">
            <h3>配置设置</h3>
            <label>Supabase URL:</label>
            <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co" style="width: 100%; margin: 5px 0; padding: 8px;">
            
            <label>API Key:</label>
            <input type="text" id="apiKey" placeholder="your-anon-key" style="width: 100%; margin: 5px 0; padding: 8px;">
            
            <label>代理URL (可选):</label>
            <input type="text" id="proxyUrl" placeholder="https://your-worker.workers.dev" style="width: 100%; margin: 5px 0; padding: 8px;">
        </div>

        <div class="test-item">
            <h3>连接测试</h3>
            <button onclick="testDirectConnection()">测试直连</button>
            <button onclick="testProxyConnection()">测试代理连接</button>
            <button onclick="testBothConnections()">对比测试</button>
            <button onclick="clearResults()">清除结果</button>
            
            <div id="testResults"></div>
        </div>

        <div class="stats" id="statsContainer" style="display: none;">
            <div class="stat-card">
                <h4>平均延迟</h4>
                <div id="avgLatency">-</div>
            </div>
            <div class="stat-card">
                <h4>成功率</h4>
                <div id="successRate">-</div>
            </div>
            <div class="stat-card">
                <h4>最佳方案</h4>
                <div id="bestOption">-</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        let testResults = [];

        async function testConnection(url, key, label, isProxy = false) {
            const resultDiv = document.getElementById('testResults');
            const testId = Date.now();
            
            // 添加测试状态显示
            const statusDiv = document.createElement('div');
            statusDiv.className = 'result';
            statusDiv.innerHTML = `
                <div class="loading"></div>
                <strong>${label}</strong> - 测试中...
            `;
            resultDiv.appendChild(statusDiv);
            
            const attempts = 3;
            const results = [];
            
            for (let i = 0; i < attempts; i++) {
                try {
                    const startTime = Date.now();
                    
                    // 创建Supabase客户端
                    const supabase = window.supabase.createClient(url, key, {
                        global: {
                            fetch: (input, init) => {
                                // 添加超时控制
                                const controller = new AbortController();
                                const timeoutId = setTimeout(() => controller.abort(), 10000);
                                
                                return fetch(input, {
                                    ...init,
                                    signal: controller.signal
                                }).finally(() => clearTimeout(timeoutId));
                            }
                        }
                    });
                    
                    // 执行简单的健康检查
                    const { data, error } = await supabase
                        .from('ping')
                        .select('*')
                        .limit(1);
                    
                    const latency = Date.now() - startTime;
                    
                    if (error && !error.message.includes('permission denied') && !error.message.includes('does not exist')) {
                        throw error;
                    }
                    
                    results.push({
                        success: true,
                        latency: latency,
                        attempt: i + 1
                    });
                    
                } catch (error) {
                    const latency = Date.now() - startTime;
                    results.push({
                        success: false,
                        latency: latency,
                        error: error.message,
                        attempt: i + 1
                    });
                }
            }
            
            // 计算统计信息
            const successfulTests = results.filter(r => r.success);
            const avgLatency = successfulTests.length > 0 
                ? Math.round(successfulTests.reduce((sum, r) => sum + r.latency, 0) / successfulTests.length)
                : null;
            const successRate = Math.round((successfulTests.length / attempts) * 100);
            
            // 更新显示结果
            statusDiv.innerHTML = `
                <strong>${label}</strong><br>
                <span class="${successRate > 50 ? 'success' : successRate > 0 ? 'warning' : 'error'}">
                    成功率: ${successRate}% ${avgLatency ? `| 平均延迟: ${avgLatency}ms` : ''}
                </span><br>
                <small>详细: ${results.map(r => 
                    `第${r.attempt}次: ${r.success ? r.latency + 'ms' : '失败'}`
                ).join(', ')}</small>
            `;
            
            // 保存结果用于统计
            testResults.push({
                label,
                isProxy,
                avgLatency,
                successRate,
                timestamp: new Date()
            });
            
            updateStats();
            
            return { avgLatency, successRate, results };
        }

        async function testDirectConnection() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('apiKey').value;
            
            if (!url || !key) {
                alert('请填写Supabase URL和API Key');
                return;
            }
            
            await testConnection(url, key, '🔗 直连测试');
        }

        async function testProxyConnection() {
            const proxyUrl = document.getElementById('proxyUrl').value;
            const key = document.getElementById('apiKey').value;
            
            if (!proxyUrl || !key) {
                alert('请填写代理URL和API Key');
                return;
            }
            
            await testConnection(proxyUrl, key, '🛡️ 代理测试', true);
        }

        async function testBothConnections() {
            const url = document.getElementById('supabaseUrl').value;
            const proxyUrl = document.getElementById('proxyUrl').value;
            const key = document.getElementById('apiKey').value;
            
            if (!url || !key) {
                alert('请填写Supabase URL和API Key');
                return;
            }
            
            // 清除之前的结果
            document.getElementById('testResults').innerHTML = '';
            testResults = [];
            
            // 测试直连
            const directResult = await testConnection(url, key, '🔗 直连测试');
            
            // 如果有代理URL，测试代理连接
            if (proxyUrl) {
                const proxyResult = await testConnection(proxyUrl, key, '🛡️ 代理测试', true);
                
                // 显示对比结果
                const resultDiv = document.getElementById('testResults');
                const comparisonDiv = document.createElement('div');
                comparisonDiv.className = 'result';
                comparisonDiv.style.marginTop = '20px';
                comparisonDiv.style.padding = '15px';
                comparisonDiv.style.background = '#e9ecef';
                comparisonDiv.style.borderRadius = '5px';
                
                let recommendation = '';
                if (proxyResult.successRate > directResult.successRate) {
                    recommendation = '🎉 推荐使用代理连接！';
                } else if (proxyResult.avgLatency && directResult.avgLatency && proxyResult.avgLatency < directResult.avgLatency) {
                    recommendation = '⚡ 代理连接速度更快！';
                } else {
                    recommendation = '📊 直连表现更好或相当';
                }
                
                comparisonDiv.innerHTML = `
                    <h4>📈 对比分析</h4>
                    <p><strong>${recommendation}</strong></p>
                    <p>性能提升: ${
                        directResult.avgLatency && proxyResult.avgLatency 
                        ? Math.round(((directResult.avgLatency - proxyResult.avgLatency) / directResult.avgLatency) * 100) + '%'
                        : '无法计算'
                    }</p>
                `;
                
                resultDiv.appendChild(comparisonDiv);
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('statsContainer').style.display = 'none';
            testResults = [];
        }

        function updateStats() {
            if (testResults.length === 0) return;
            
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.style.display = 'grid';
            
            // 计算平均延迟
            const validResults = testResults.filter(r => r.avgLatency);
            const avgLatency = validResults.length > 0 
                ? Math.round(validResults.reduce((sum, r) => sum + r.avgLatency, 0) / validResults.length)
                : 0;
            
            // 计算整体成功率
            const avgSuccessRate = Math.round(
                testResults.reduce((sum, r) => sum + r.successRate, 0) / testResults.length
            );
            
            // 找出最佳选项
            const proxyResults = testResults.filter(r => r.isProxy);
            const directResults = testResults.filter(r => !r.isProxy);
            
            let bestOption = '暂无数据';
            if (proxyResults.length > 0 && directResults.length > 0) {
                const proxyAvg = proxyResults[proxyResults.length - 1];
                const directAvg = directResults[directResults.length - 1];
                
                if (proxyAvg.successRate > directAvg.successRate) {
                    bestOption = '代理连接';
                } else if (proxyAvg.avgLatency && directAvg.avgLatency && proxyAvg.avgLatency < directAvg.avgLatency) {
                    bestOption = '代理连接';
                } else {
                    bestOption = '直接连接';
                }
            }
            
            document.getElementById('avgLatency').textContent = avgLatency + 'ms';
            document.getElementById('successRate').textContent = avgSuccessRate + '%';
            document.getElementById('bestOption').textContent = bestOption;
        }

        // 页面加载时的提示
        window.addEventListener('load', function() {
            console.log('🚀 Supabase连接测试工具已就绪');
            console.log('💡 请填写您的Supabase配置信息开始测试');
        });
    </script>
</body>
</html> 