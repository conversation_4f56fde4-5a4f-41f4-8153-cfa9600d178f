@echo off
REM Windows 兼容的文件复制脚本
echo 📋 开始复制 Claude Code 包文件...

if exist "temp-claude-package\package\cli.js" (
    echo 复制 cli.js...
    copy "temp-claude-package\package\cli.js" "cli.js"
)

if exist "temp-claude-package\package\yoga.wasm" (
    echo 复制 yoga.wasm...
    copy "temp-claude-package\package\yoga.wasm" "yoga.wasm"
)

if exist "temp-claude-package\package\vendor" (
    echo 复制 vendor 目录...
    robocopy "temp-claude-package\package\vendor" "vendor" /E /NFL /NDL /NJH /NJS
    if errorlevel 8 (
        echo 使用 xcopy 作为备选方案...
        xcopy "temp-claude-package\package\vendor" "vendor" /E /I /Y
    )
)

echo ✅ 文件复制完成
