/**
 * 优化的Supabase配置
 * 专门针对国内网络环境进行优化
 */

import { createClient } from '@supabase/supabase-js'

// Supabase配置常量
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co'
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'

// 代理URL（如果使用Cloudflare Worker代理）
const PROXY_URL = process.env.NEXT_PUBLIC_SUPABASE_PROXY_URL || SUPABASE_URL

/**
 * 优化的Supabase客户端配置
 */
const supabaseConfig = {
  auth: {
    // 启用自动刷新
    autoRefreshToken: true,
    // 检测会话变化
    detectSessionInUrl: true,
    // 持久化会话
    persistSession: true,
    // 存储密钥
    storageKey: 'supabase.auth.token'
  },
  
  // 全局配置
  global: {
    // 请求头配置
    headers: {
      'x-client-info': 'supabase-js-web',
      'Cache-Control': 'max-age=3600', // 缓存1小时
    },
    
    // 自定义fetch配置
    fetch: (url, options = {}) => {
      // 添加超时设置
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时')), 15000) // 15秒超时
      })
      
      // 优化的fetch配置
      const fetchOptions = {
        ...options,
        // 启用keep-alive
        keepalive: true,
        // 优化缓存策略
        cache: options.method === 'GET' ? 'force-cache' : 'no-cache',
      }
      
      const fetchPromise = fetch(url, fetchOptions)
      
      return Promise.race([fetchPromise, timeoutPromise])
    }
  },
  
  // 数据库配置
  db: {
    // 连接池设置
    schema: 'public'
  },
  
  // 实时订阅配置
  realtime: {
    // 启用心跳检测
    heartbeatIntervalMs: 30000,
    // 重连配置
    reconnectAfterMs: function (tries) {
      return Math.min(tries * 1000, 10000) // 最大10秒重连间隔
    }
  }
}

/**
 * 创建优化的Supabase客户端
 */
export const supabase = createClient(PROXY_URL, SUPABASE_ANON_KEY, supabaseConfig)

/**
 * 缓存管理器
 */
class SupabaseCache {
  constructor() {
    this.cache = new Map()
    this.ttl = 5 * 60 * 1000 // 5分钟TTL
  }
  
  /**
   * 获取缓存的查询结果
   * @param {string} key - 缓存键
   * @returns {any} 缓存的数据或null
   */
  get(key) {
    const item = this.cache.get(key)
    if (!item) return null
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return item.data
  }
  
  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 要缓存的数据
   */
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  /**
   * 清除所有缓存
   */
  clear() {
    this.cache.clear()
  }
}

// 全局缓存实例
export const cache = new SupabaseCache()

/**
 * 带缓存的查询函数
 * @param {string} table - 表名
 * @param {object} query - 查询条件
 * @param {boolean} useCache - 是否使用缓存
 * @returns {Promise} 查询结果
 */
export async function cachedQuery(table, query = {}, useCache = true) {
  const cacheKey = `${table}-${JSON.stringify(query)}`
  
  // 检查缓存
  if (useCache) {
    const cached = cache.get(cacheKey)
    if (cached) {
      console.log('📦 使用缓存数据:', cacheKey)
      return cached
    }
  }
  
  try {
    console.log('🌐 发送网络请求:', cacheKey)
    
    // 执行查询
    let queryBuilder = supabase.from(table).select()
    
    // 应用查询条件
    Object.entries(query).forEach(([key, value]) => {
      if (key === 'limit') {
        queryBuilder = queryBuilder.limit(value)
      } else if (key === 'order') {
        queryBuilder = queryBuilder.order(value.column, { ascending: value.ascending })
      } else if (key === 'filter') {
        Object.entries(value).forEach(([filterKey, filterValue]) => {
          queryBuilder = queryBuilder.eq(filterKey, filterValue)
        })
      }
    })
    
    const { data, error } = await queryBuilder
    
    if (error) {
      throw new Error(`查询失败: ${error.message}`)
    }
    
    // 缓存结果
    if (useCache && data) {
      cache.set(cacheKey, { data, error })
    }
    
    return { data, error }
    
  } catch (error) {
    console.error('❌ Supabase查询错误:', error)
    return { data: null, error }
  }
}

/**
 * 批量查询优化
 * @param {Array} queries - 查询数组
 * @returns {Promise} 所有查询结果
 */
export async function batchQuery(queries) {
  const promises = queries.map(({ table, query, useCache }) => 
    cachedQuery(table, query, useCache)
  )
  
  try {
    const results = await Promise.allSettled(promises)
    return results.map(result => 
      result.status === 'fulfilled' ? result.value : { data: null, error: result.reason }
    )
  } catch (error) {
    console.error('❌ 批量查询失败:', error)
    return queries.map(() => ({ data: null, error }))
  }
}

/**
 * 连接状态检测
 */
export async function checkConnection() {
  try {
    const start = Date.now()
    const { data, error } = await supabase.from('profiles').select('id').limit(1)
    const latency = Date.now() - start
    
    if (error) {
      return { connected: false, latency: null, error: error.message }
    }
    
    return { connected: true, latency, error: null }
  } catch (error) {
    return { connected: false, latency: null, error: error.message }
  }
}

// 导出默认配置
export default {
  supabase,
  cache,
  cachedQuery,
  batchQuery,
  checkConnection
} 