--------------------------------------------------


请帮我开发一款全中文界面的提示词管理工具，具体要求如下：

**技术栈要求：**
- 后端：PHP 8.2+ + Laravel 10+
- 前端：Vue.js 3 + Composition API + TypeScript
- 状态管理：Pinia
- 构建工具：Vite
- UI框架：Tailwind CSS + DaisyUI + Headless UI Vue
- 数据库：MySQL 8.0
- API：Laravel API Resources + Sanctum认证
- 架构：前后端分离 (SPA)

请为我开发一个提示词管理系统，具体功能需求如下：

**核心功能：**
1. **提示词展示**：
   - 以响应式卡片网格布局展示提示词列表
   - 每个卡片显示：标题、简短描述、分类标签、使用次数
   - 支持卡片悬停效果和点击交互

2. **详情查看**：
   - 点击卡片弹出模态框或跳转详情页
   - 显示完整提示词内容、创建时间、最后修改时间
   - 支持内容格式化显示（如代码高亮）

3. **一键复制**：
   - 每个提示词卡片和详情页都有复制按钮
   - 复制成功后显示Toast提示
   - 复制完整提示词内容到剪贴板

4. **分类管理**：
   - 支持创建、编辑、删除分类
   - 分类筛选功能，可按分类查看提示词
   - 分类颜色标识和图标支持

5. **搜索功能**：
   - 实时搜索框，支持按标题、内容、标签模糊搜索
   - 搜索结果高亮显示匹配关键词
   - 搜索历史记录功能

6. **编辑功能**：
   - 支持在线编辑提示词标题、内容、分类、标签
   - 提供Markdown编辑器或富文本编辑器
   - 编辑时自动保存草稿功能

7. **新增功能**：
   - 提供"添加新提示词"按钮和表单
   - 支持批量导入提示词（JSON格式）
   - 表单验证和错误提示

8. **统计功能**：
   - 每个提示词卡片显示使用次数徽章
   - 点击复制时自动增加使用计数
   - 提供使用统计排行榜

**技术要求：**
- 使用现代前端框架
- 响应式设计，支持移动端
- 数据持久化存储（本地存储或数据库）
- 良好的用户体验和界面设计
- 代码结构清晰，遵循最佳实践

**交付物：**
- 完整的前端应用代码
- 数据结构设计
- 使用说明文档
- 如需要，提供简单的后端API

**数据库连接信息：**
- 数据库名：tishici-php
- 用户名：root
- 密码：9qdqn7qg
- 主机：tishici-php-mysql.ns-qqzrupud.svc
- 端口：3306

**部署信息：**
- 公网调试地址：https://bnpgznsbnmuu.sealosgzg.site

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7.请使用公网地址访问应用

请按照任务管理流程创建详细的开发计划，并逐步实现所有功能。


--------------------------------------------------


请帮我开发一款个人使用，精美的现代化界面，全中文的提示词管理工具，具体要求如下：

**技术栈要求：**

### 前端技术栈
- **核心框架**: Next.js 15
- **开发语言**: TypeScript
- **UI样式系统**: Tailwind CSS V4
- **UI组件库**: daisyUI V5（参考https://daisyui.com/docs/）
- **状态管理**: Zustand
- **数据获取**: tRPC
- **编辑器**: @uiw/react-textarea-code-editor
- **动画库**: Framer Motion
- **图标库**: Font Awesome

### 后端技术栈
- **服务框架**: Next.js API Routes
- **API架构**: tRPC路由器
- **数据库ORM**: Prisma
- **主数据库**: PostgreSQL
- **用户认证**: NextAuth.js v5 (支持OAuth/邮箱登录)

### 界面语言
- 中文简体

**核心功能：**
1. **提示词展示**：
   - 以响应式卡片网格布局展示提示词列表
   - 每个卡片显示：标题、简短描述、分类标签、使用次数
   - 支持卡片悬停效果和点击交互

2. **详情查看**：
   - 点击卡片弹出模态框
   - 显示完整提示词内容、创建时间、最后修改时间
   - 支持内容格式化显示（如代码高亮）

3. **一键复制**：
   - 每个提示词卡片和详情页都有复制按钮
   - 复制成功后显示Toast提示
   - 复制完整提示词内容到剪贴板

4. **分类管理**：
   - 直观的分类导航，左侧边栏分类目录树
   - 支持创建、编辑、删除分类
   - 分类筛选功能，可按分类查看提示词
   - 分类颜色标识和图标支持

5. **搜索功能**：
   - 实时搜索框，支持按标题、内容、标签模糊搜索
   - 搜索结果高亮显示匹配关键词
   - 搜索历史记录功能

6. **编辑功能**：
   - 支持编辑提示词标题、内容、分类、标签
   - 提供Markdown编辑器

7. **新增功能**：
   - 提供"添加新提示词"按钮和表单
   - 支持批量导入提示词（JSON格式）

8. **统计功能**：
   - 每个提示词卡片显示使用次数
   - 点击复制时自动增加使用计数

9. **UI界面设计**：
   - 简洁的布局，充足的空间感
   - 使用 daisyUI 组件，搭配多彩柔和色（避免渐变）
   - 丰富的悬停效果和微动画
   - 响应式优先，完美适配各种设备尺寸
   - 清晰的视觉层次和信息优先级

**技术要求：**
- 使用现代前端框架
- 响应式设计，支持移动端
- 数据持久化存储（本地存储或数据库）
- 良好的用户体验和界面设计
- 代码结构清晰，遵循最佳实践

**交付物：**
- 完整的前端应用
- 数据结构设计
- 使用说明文档
- 如需要，提供简单的后端API说明

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7.请使用公网地址访问应用

请按照任务管理流程创建详细的开发计划和任务列表，并逐步实现所有功能。

**数据库连接信息：**
- 数据库名：tishici2
- 用户名：postgres
- 密码：rh2tl4k2
- 主机：dbconn.sealosgzg.site
- 端口：34780

**部署信息：**
- 公网调试地址：https://qazvexhgtbyk.sealosgzg.site


--------------------------------------------------