请帮我开发一个现代化的短剧资源搜索与播放网站，要求超越参考网站 https://moyu.clbug.com/drama.html 的用户体验和技术水平。

**技术要求：**
- 使用现代前端框架（推荐 React + Next.js）
- 采用 TypeScript 提升代码质量
- 使用现代 CSS 框架（如 Tailwind CSS）实现响应式设计
- 实现 PWA（渐进式Web应用）支持离线访问
- 优化性能：代码分割、懒加载、图片优化等
- 支持 SSR/SSG 提升首屏加载速度

**设计要求：**
- 完美适配移动端（优先移动端设计）
- 现代简约的 UI 设计，具有高级感
- 流畅的动画过渡效果
- 直观的用户交互体验

**核心功能：**
1. **短剧搜索**：集成 API https://api.xingzhige.com/doc/56
   - 实时搜索建议
   - 搜索历史记录
   - 热门搜索推荐
   - 分类筛选功能

2. **在线播放**：
   - 自适应视频播放器
   - 多清晰度切换（720p/1080p/原画）
   - 播放进度记忆
   - 全屏播放支持
   - 倍速播放功能
   - 集数选择功能

3. **观看历史**：
   - 本地存储观看记录
   - 继续观看功能
   - 历史记录管理（删除、清空）

4. **用户体验优化**：
   - 骨架屏加载效果
   - 错误边界处理
   - 网络状态检测
   - 无限滚动加载

**性能目标：**
- 首屏加载时间 < 2秒
- Lighthouse 性能评分 > 90
- 支持低网速环境使用

**项目结构要求：**
- 模块化组件设计
- 统一的状态管理
- 完善的错误处理机制
- 代码注释和文档

请创建完整的项目结构，包括所有必要的配置文件、组件、页面和样式，确保项目可以直接运行和部署。