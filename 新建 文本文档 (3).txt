以当前目录的项目模板为基础，请帮我开发一款个人使用，不需要注册即开即用的，全中文的提示词管理工具，具体要求如下：

**技术栈：**
   - Node.js + TypeScript + Tailwind CSS + PostgreSQL + ShadcnUI组件 + Font Awesome图标库

**核心功能：**
1. **提示词展示**：
   - 以响应式卡片网格布局展示提示词列表
   - 每个卡片显示：标题、简短描述、分类、标签、使用次数
   - 支持卡片悬停效果和点击交互

2. **详情查看**：
   - 点击提示词卡片弹出模态框
   - 显示完整提示词内容、创建时间、最后修改时间
   - 提示词内容支持Markdown格式化显示（如代码高亮，代码一键复制）

3. **一键复制**：
   - 每个提示词卡片和详情页都有复制按钮
   - 复制成功后显示Toast提示
   - 复制完整提示词内容到剪贴板

4. **分类管理**：
   - 直观的分类导航，左侧边栏分类目录树
   - 支持创建、编辑、删除分类
   - 支持拖拽排序分类顺序
   - 分类筛选功能，可按分类查看提示词
   - 分类颜色标识和Font Awesome图标支持

5. **搜索功能**：
   - 实时搜索框，支持按标题、内容、标签模糊搜索
   - 搜索结果高亮显示匹配关键词
   - 搜索历史记录功能

6. **编辑功能**：
   - 支持编辑提示词标题、内容、分类、标签
   - 提示词内容提供轻量Markdown编辑器

7. **新增功能**：
   - 提供"添加新提示词"按钮和表单
   - 支持批量导入提示词（JSON格式）

8. **统计功能**：
   - 每个提示词卡片显示使用次数
   - 点击复制时自动增加使用计数

9. **UI界面设计**：
   - 简洁的布局，但要有高级感，充足的空间感
   - 主体颜色以蓝色和黑色为主，搭配多彩柔和色（禁止使用渐变颜色）
   - 使用 Font Awesome 图标管理
   - 使用 ShadcnUI 组件
   - 丰富的悬停效果和微交互动画
   - 响应式优先，完美适配各种设备尺寸
   - 清晰的视觉层次和信息优先级

**技术要求：**
- 使用现代前端框架
- 响应式设计，完美适配移动端
- 数据持久化存储（Postgres）
- 精美的用户体验和界面设计
- 代码结构清晰，遵循最佳实践

**交付物：**
- 完整的前端应用
- 数据结构设计
- 使用说明文档
- 如需要，提供简单的后端API说明

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
