# 🏮 仙途无界 - 修仙文字RPG游戏

> 一款以中国古典修仙文化为背景的单机版纯文字RPG游戏

[![技术栈](https://img.shields.io/badge/技术栈-Vue3%20%2B%20TypeScript%20%2B%20Supabase-brightgreen)](https://github.com/your-username/RPG)
[![开发状态](https://img.shields.io/badge/开发状态-规划中-yellow)](docs/开发进度.md)
[![许可证](https://img.shields.io/badge/许可证-MIT-blue)](LICENSE)

## 📖 项目简介

《仙途无界》是一款深度文字RPG游戏，玩家将在修仙世界中经历从凡人到仙人的完整修炼历程。游戏采用现代Web技术开发，使用Supabase作为后端服务，提供丰富的职业选择、深度的修仙体系和30个章节的完整剧情。

### ✨ 核心特色

- 🎭 **六大职业**: 剑修、体修、丹修、阵修、法修、兽修
- 🏔️ **完整修仙体系**: 从练气到飞升的10个境界
- 📚 **30章节剧情**: 4大卷，丰富的剧情分支
- 🎲 **10种结局**: 根据选择获得不同人生结局
- ⚔️ **策略战斗**: 回合制战斗系统
- 🎒 **深度装备**: 品质、强化、镶嵌系统
- 💫 **职业专属**: 每个职业都有独特的游戏体验

## 🎮 游戏特性

### 角色系统
- **灵根系统**: 五行灵根 + 变异灵根 + 特殊灵根
- **属性成长**: 力敏智体感魅 + 修仙专属属性
- **技能学习**: 职业专属技能树

### 游戏世界
- **宗门势力**: 正道三大宗 + 魔道五大派
- **修仙境界**: 练气→筑基→金丹→元婴→化神→炼虚→合体→大乘→渡劫
- **丰富NPC**: 师父、同门、道侣、宿敌等重要角色

### 游戏机制
- **多重选择**: 每章节3-5个重要选择点
- **职业差异**: 同一剧情不同职业有不同选项
- **关系系统**: NPC好感度影响剧情发展
- **成就系统**: 丰富的成就和收集要素

## 🛠️ 技术架构

### 前端技术栈
```
Vue.js 3 + TypeScript
Pinia (状态管理)
Vue Router 4 (路由)
Element Plus (UI组件)
Tailwind CSS (样式)
Vite (构建工具)
```

### 后端服务
```
Supabase PostgreSQL (数据库)
Supabase Auth (用户认证)
Supabase Realtime (实时功能)
Supabase Storage (文件存储)
```

### 项目结构
```
RPG/
├── docs/           # 项目文档
├── src/            # 前端源代码
├── database/       # 数据库文件
├── tests/          # 测试文件
└── scripts/        # 构建脚本
```

## 📋 开发计划

### 开发阶段
- [x] **需求分析** - 完成详细需求文档
- [ ] **第一阶段** (1-3周) - 基础框架搭建
- [ ] **第二阶段** (4-8周) - 核心系统开发
- [ ] **第三阶段** (9-14周) - 剧情内容实现
- [ ] **第四阶段** (15-18周) - 优化完善

### 关键里程碑
1. **基础可玩版本** (第3周) - 角色创建和基础操作
2. **战斗系统完成** (第6周) - 完整战斗体验
3. **剧情框架完成** (第9周) - 第一章完整可玩
4. **中期版本** (第12周) - 前半部分内容完整
5. **完整版本** (第18周) - 全部30章内容完成

## 📚 文档

- [📋 项目需求文档](docs/项目需求文档.md) - 完整的游戏设计文档
- [📁 项目目录结构](docs/项目目录结构.md) - 详细的项目结构说明
- [🗃️ 数据库设计](docs/数据库设计.md) - 数据库表结构设计
- [📊 开发进度](docs/开发进度.md) - 实时开发进度跟踪

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn
- Supabase账号

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/RPG.git
cd RPG
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填入你的Supabase配置
```

4. **启动开发服务器**
```bash
npm run dev
```

5. **访问应用**
打开浏览器访问 `http://localhost:5173`

## 🎯 游戏剧情概览

### 第一卷：踏入修仙路 (1-8章)
从平凡少年觉醒灵根开始，加入宗门学习修仙之道，经历第一次历练和筑基突破。

### 第二卷：修仙界风云 (9-16章)
深入修仙界，学习副职业，参与正魔大战，面临师门危机。

### 第三卷：称雄一方 (17-24章)
成为元婴强者，开山立派，传道授业，争夺修仙界霸权。

### 第四卷：问鼎飞升 (25-30章)
面临天劫考验，做出最终选择，走向不同的人生结局。

## 🎮 职业介绍

| 职业 | 特点 | 主属性 | 专长 |
|------|------|--------|------|
| 🗡️ 剑修 | 以剑为道 | 力量、敏捷 | 单体爆发最强 |
| 💪 体修 | 炼体强身 | 体质、力量 | 血厚防高坦克 |
| 💊 丹修 | 炼丹济世 | 智力、感知 | 辅助治疗专家 |
| 🔮 阵修 | 精通阵法 | 智力、感知 | 群体控制大师 |
| ⚡ 法修 | 法术专精 | 智力、感知 | 元素法术输出 |
| 🐺 兽修 | 御兽之道 | 魅力、感知 | 召唤作战策略 |

## 🏆 成就系统

- **修炼成就**: 境界突破、修为里程碑
- **战斗成就**: 连胜记录、技能使用
- **剧情成就**: 特殊选择、隐藏剧情
- **收集成就**: 装备收集、图鉴完成
- **关系成就**: NPC好感度、特殊关系

## 🤝 贡献指南

欢迎为项目贡献代码！请查看 [贡献指南](CONTRIBUTING.md) 了解详细信息。

### 如何贡献
1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目维护者: [@YourUsername](https://github.com/YourUsername)
- 项目地址: [https://github.com/YourUsername/RPG](https://github.com/YourUsername/RPG)
- 问题反馈: [Issues](https://github.com/YourUsername/RPG/issues)

## 🌟 支持项目

如果这个项目对你有帮助，请给它一个 ⭐️！

---

<div align="center">
  <img src="https://img.shields.io/badge/Made%20with-❤️-red.svg"/>
  <img src="https://img.shields.io/badge/Vue.js-35495E?style=flat&logo=vue.js&logoColor=4FC08D"/>
  <img src="https://img.shields.io/badge/TypeScript-007ACC?style=flat&logo=typescript&logoColor=white"/>
  <img src="https://img.shields.io/badge/Supabase-3ECF8E?style=flat&logo=supabase&logoColor=white"/>
</div> 