<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百家乐路单图详细说明</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .road-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .road-grid {
            display: inline-block;
            margin: 15px;
            border: 2px solid #333;
            background-color: #000;
        }
        .road-grid table {
            border-collapse: collapse;
            background-color: white;
        }
        .road-grid td {
            width: 30px;
            height: 30px;
            border: 1px solid #666;
            text-align: center;
            vertical-align: middle;
            font-size: 14px;
            font-weight: bold;
            position: relative;
        }
        .banker {
            background-color: #ff4444;
            color: white;
        }
        .player {
            background-color: #4444ff;
            color: white;
        }
        .tie {
            background-color: #44aa44;
            color: white;
        }
        .red-circle {
            background-color: white;
            border: 3px solid #ff4444;
            border-radius: 50%;
            color: #ff4444;
        }
        .blue-circle {
            background-color: white;
            border: 3px solid #4444ff;
            border-radius: 50%;
            color: #4444ff;
        }
        .tie-mark::after {
            content: "/";
            position: absolute;
            top: 2px;
            left: 12px;
            color: #44aa44;
            font-size: 20px;
            font-weight: bold;
        }
        .pair-dot::before {
            content: "•";
            position: absolute;
            font-size: 16px;
        }
        .banker-pair::before {
            color: #ff4444;
            top: -5px;
            left: 2px;
        }
        .player-pair::before {
            color: #4444ff;
            bottom: -5px;
            right: 2px;
        }
        .red-derived {
            background-color: #ff6666;
            color: white;
        }
        .blue-derived {
            background-color: #6666ff;
            color: white;
        }
        .prediction-table {
            display: inline-block;
            margin: 15px;
            border: 2px solid #333;
        }
        .prediction-table table {
            border-collapse: collapse;
        }
        .prediction-table td {
            width: 40px;
            height: 30px;
            border: 1px solid #333;
            text-align: center;
            font-weight: bold;
        }
        .rules-list {
            background-color: #ecf0f1;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .rules-list ul {
            margin: 0;
        }
        .example-data {
            background-color: #e8f4fd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>百家乐路单图详细说明</h1>
        <p><strong>百家乐路单</strong>是记录和分析游戏结果趋势的工具，主要用于识别连胜、交替等模式。路单起源于澳门赌场，最初用纸笔记录，现已发展为电子显示。本页面详细说明各种路单的生成规则、标记方式和使用方法。</p>

        <div class="road-section">
            <h2>1. 珠盘路（Bead Plate / 珠仔路）</h2>
            <h3>生成依据：</h3>
            <p>直接记录鞋子中每手的实际结果，无任何派生或分析。从左上角开始，垂直向下填充每列（6格），满列后移到下一列顶部。</p>
            
            <h3>标记方式：</h3>
            <div class="rules-list">
                <ul>
                    <li><span style="color: red;">●</span> 庄家胜：红色实心圆（标记B）</li>
                    <li><span style="color: blue;">●</span> 闲家胜：蓝色实心圆（标记P）</li>
                    <li><span style="color: green;">●</span> 和局：绿色实心圆（标记T）</li>
                </ul>
            </div>

            <h3>示例：</h3>
            <div class="example-data">示例结果序列：庄-闲-闲-庄-和-庄-闲-庄-闲-庄-和-闲</div>
            
            <div class="road-grid">
                <table>
                    <tr><td class="banker">B</td><td class="player">P</td></tr>
                    <tr><td class="player">P</td><td class="banker">B</td></tr>
                    <tr><td class="player">P</td><td class="player">P</td></tr>
                    <tr><td class="banker">B</td><td class="banker">B</td></tr>
                    <tr><td class="tie">T</td><td class="tie">T</td></tr>
                    <tr><td class="banker">B</td><td class="player">P</td></tr>
                </table>
            </div>
        </div>

        <div class="road-section">
            <h2>2. 大路（Big Road / 大路图）</h2>
            <h3>生成依据：</h3>
            <p>所有路单的基础，记录连胜和交替模式。当结果从庄换到闲（或反之）时开始新列。重点分析连胜长度和交替频率。</p>
            
            <h3>标记方式：</h3>
            <div class="rules-list">
                <ul>
                    <li>🔴 庄家胜：红色空心圆圈</li>
                    <li>🔵 闲家胜：蓝色空心圆圈</li>
                    <li>/ 和局：绿色斜线（划过前一个圆圈，多个和局用数字表示）</li>
                    <li>• 对子：庄对子（红点，左上角），闲对子（蓝点，右下角）</li>
                    <li>连胜超过6手时形成"龙"，水平向右延伸</li>
                </ul>
            </div>

            <h3>示例：</h3>
            <div class="example-data">示例结果：庄-庄-闲-闲-闲-庄-和-庄-庄</div>
            
            <div class="road-grid">
                <table>
                    <tr><td class="red-circle">○</td><td class="blue-circle">○</td><td class="red-circle tie-mark">○</td></tr>
                    <tr><td class="red-circle">○</td><td class="blue-circle">○</td><td class="red-circle">○</td></tr>
                    <tr><td></td><td class="blue-circle">○</td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                </table>
            </div>
            <p><small>第一列：庄庄（2连红）→ 第二列：闲闲闲（3连蓝）→ 第三列：庄和庄（红圈上有绿色斜线表示和局）</small></p>
        </div>

        <div class="road-section">
            <h2>3. 大眼仔路（Big Eye Boy / 大眼路）</h2>
            <h3>生成依据：</h3>
            <p>从大路衍生，分析大路的"规律性"。红色表示有序（模式），蓝色表示混乱（无规律）。在大路第二列第一手后开始记录。</p>
            
            <h3>生成规则：</h3>
            <div class="rules-list">
                <ul>
                    <li><strong>新列时：</strong>比较大路前两列深度，相同→红，不同→蓝</li>
                    <li><strong>继续列时：</strong>比较新格左边格子与其上方格子，相同颜色→红，不同→蓝</li>
                    <li>起始：大路第二列第一手结果后</li>
                </ul>
            </div>

            <h3>示例：</h3>
            <div class="road-grid">
                <table>
                    <tr><td class="blue-derived">○</td><td class="red-derived">○</td><td></td></tr>
                    <tr><td class="red-derived">○</td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                </table>
            </div>
            <p><small>基于上述大路示例：第一列2个，第二列3个（不同深度→蓝）；第二列继续时比较相邻格...</small></p>
        </div>

        <div class="road-section">
            <h2>4. 小路（Small Road）</h2>
            <h3>生成依据：</h3>
            <p>类似大眼仔路，但比较间隔更远的列（跳过一列）。在大路第三列第一手后开始记录。</p>
            
            <h3>生成规则：</h3>
            <div class="rules-list">
                <ul>
                    <li><strong>新列时：</strong>比较大路新列左边第1列和第3列深度</li>
                    <li><strong>继续列时：</strong>比较新格左边第2个格子与其上方</li>
                    <li>标记：红蓝实心圆点</li>
                </ul>
            </div>

            <h3>示例：</h3>
            <div class="road-grid">
                <table>
                    <tr><td class="red-derived">●</td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                </table>
            </div>
        </div>

        <div class="road-section">
            <h2>5. 曱甴路（Cockroach Road / 小强路）</h2>
            <h3>生成依据：</h3>
            <p>比较间隔最远的列（跳过两列）。在大路第四列第一手后开始记录。用于确认长期模式。</p>
            
            <h3>生成规则：</h3>
            <div class="rules-list">
                <ul>
                    <li><strong>新列时：</strong>比较大路新列左边第1列和第4列深度</li>
                    <li><strong>继续列时：</strong>比较新格左边第3个格子与其上方</li>
                    <li>标记：红蓝斜杠（/）</li>
                </ul>
            </div>

            <h3>示例：</h3>
            <div class="road-grid">
                <table>
                    <tr><td class="blue-derived">/</td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                    <tr><td></td><td></td><td></td></tr>
                </table>
            </div>
        </div>

        <div class="road-section">
            <h2>6. 庄问路和闲问路（Banker/Player Ask Road）</h2>
            <h3>生成依据：</h3>
            <p>预测工具，模拟下一手如果是庄（或闲）胜，各派生路单会如何变化。帮助玩家决策投注。</p>
            
            <h3>展示方式：</h3>
            <p>通常显示在电子屏右侧的小表格中（2列3行），分别显示大眼仔路、小路、曱甴路的预测结果。</p>
            
            <div class="prediction-table">
                <table>
                    <tr><td style="background-color: #ddd;">庄问</td><td style="background-color: #ddd;">闲问</td></tr>
                    <tr><td class="red-derived">○</td><td class="blue-derived">○</td></tr>
                    <tr><td class="red-derived">●</td><td class="blue-derived">●</td></tr>
                    <tr><td class="red-derived">/</td><td class="blue-derived">/</td></tr>
                </table>
            </div>
            
            <h3>使用方法：</h3>
            <div class="rules-list">
                <ul>
                    <li>红色多：表示该选择会产生更多"有序"结果，可能是好的投注选择</li>
                    <li>蓝色多：表示该选择会产生"混乱"结果，可能要避免</li>
                    <li>玩家根据"问路"结果决定投注庄还是闲</li>
                </ul>
            </div>
        </div>

        <div class="road-section">
            <h2>重要说明</h2>
            <div class="rules-list">
                <ul>
                    <li><strong>数学事实：</strong>路单不能改变百家乐的随机性，庄家优势仍为1.06%，闲家1.24%</li>
                    <li><strong>心理作用：</strong>路单主要为心理安慰和趋势跟踪，无法预测未来结果</li>
                    <li><strong>文化意义：</strong>在亚洲赌场（特别是澳门）非常流行，是游戏文化的重要组成部分</li>
                    <li><strong>实用价值：</strong>在锦标赛中可用于预测其他玩家的投注倾向</li>
                </ul>
            </div>
        </div>

        <footer style="text-align: center; margin-top: 40px; color: #7f8c8d; border-top: 1px solid #ddd; padding-top: 20px;">
            <p>本页面仅供学习和了解百家乐路单规则，请理性娱乐，远离赌博成瘾。</p>
        </footer>
    </div>
</body>
</html> 