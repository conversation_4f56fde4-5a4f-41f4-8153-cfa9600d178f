/**
 * Cloudflare Worker - Supabase代理脚本
 * 用于加速国内访问Supabase的速度
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

/**
 * 处理所有请求的主函数
 * @param {Request} request - 传入的请求对象
 */
async function handleRequest(request) {
  // 您的Supabase项目URL
  const SUPABASE_URL = 'https://your-project.supabase.co'
  
  // 解析请求URL
  const url = new URL(request.url)
  
  // 构建目标URL - 将请求转发到Supabase
  const targetUrl = SUPABASE_URL + url.pathname + url.search
  
  // 复制原始请求的headers
  const modifiedHeaders = new Headers(request.headers)
  
  // 添加CORS headers
  modifiedHeaders.set('Access-Control-Allow-Origin', '*')
  modifiedHeaders.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  modifiedHeaders.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, x-client-info')
  
  // 处理预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey, x-client-info',
        'Access-Control-Max-Age': '86400',
      }
    })
  }
  
  try {
    // 创建新的请求对象
    const modifiedRequest = new Request(targetUrl, {
      method: request.method,
      headers: modifiedHeaders,
      body: request.body
    })
    
    // 发送请求到Supabase
    const response = await fetch(modifiedRequest)
    
    // 复制响应headers
    const responseHeaders = new Headers(response.headers)
    responseHeaders.set('Access-Control-Allow-Origin', '*')
    
    // 返回修改后的响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders
    })
    
  } catch (error) {
    return new Response('代理请求失败: ' + error.message, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'text/plain; charset=utf-8'
      }
    })
  }
} 