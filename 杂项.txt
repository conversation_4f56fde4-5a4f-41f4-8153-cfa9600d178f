请先查看 improvement-plan.md 文件内容，严格根据 `improvement-plan.md` 项目完善计划文档，系统性地执行以下任务：
你的任务是严格按照该计划中定义的完善计划文档的优先级、范围、技术方案和时间预估进行编码和问题修复。专注于交付高质量、符合规范、可运行的代码。

**执行要求：**
1. 首先使用工具查看 improvement-plan.md 文件的完整内容
2. 基于文档内容创建详细的任务计划，使用任务管理工具进行规划和跟踪
3. 严格按照文档中的优先级顺序执行任务：
   - 🔴 高优先级任务（第一阶段 - 1周内）
   - 🟡 中优先级任务（第二阶段 - 2-3周内）  
   - 🟢 低优先级任务（第三/四阶段 - 按需实施）

**核心完善领域（按improvement-plan.md文档定义）：**
1. **技术债务清理** - TypeScript错误修复、ESLint问题解决、代码结构优化
2. **功能增强计划** - 音效系统、数据分析、社交功能开发
3. **用户体验优化** - 界面动画、响应式设计、用户引导改进
4. **性能与稳定性** - 性能优化、错误处理、数据安全加强
5. **内容扩展规划** - 新游戏模式、内容管理系统
6. **开发工具优化** - 开发环境改进、文档完善

**代码质量要求：**
- 修复所有TypeScript类型错误（特别是BattleLog.vue、InventoryView.vue、StoryView.vue中的约140个错误）
- 解决ESLint配置和代码规范问题
- 确保代码符合企业级标准，具备生产环境部署能力
- 每个修改都要考虑对整个系统的影响，避免引入新的问题

**工作流程：**
- 按照 improvement-plan.md 中的优先级顺序执行
- 在修改代码前，先使用工具了解相关代码结构和依赖关系
- 遵循现有的代码风格和架构模式
- 每完成一个主要任务或里程碑，立即更新 improvement-plan.md 文件中的进度状态
- 使用任务管理工具跟踪进度，确保每个子任务都有明确的完成标准
- 为重要功能添加单元测试，确保代码质量
- 遵循最小改动原则，保持系统稳定性

**最终目标：** 将项目从当前MVP状态提升为生产就绪的企业级应用，确保代码质量、用户体验和系统稳定性的全面提升。

------------------------------------------------------------------------------------------------------------------------

请继续按照 improvement-plan.md 文档中的项目完善计划，有序推进项目的完善和修复工作。具体要求如下：

1. **计划执行**：
   - 严格按照 improvement-plan.md 中列出的任务优先级和顺序执行
   - 在开始每个任务前，先查看当前任务状态和依赖关系
   - 使用任务管理工具跟踪进度，及时更新任务状态

2. **代码质量要求**：
   - 遵循项目现有的编码规范和风格
   - 使用 JSDoc 注释规范为新增或修改的代码添加文档
   - 确保代码格式正确，变量命名清晰描述性强

3. **最小改动原则**：
   - 在修改代码前，先使用 codebase-retrieval 工具详细了解相关代码结构和依赖关系
   - 保持对原有功能的最小改动，避免引入新的缺陷
   - 仔细考虑所有关联模块，避免孤立地进行更改
   - 维护系统整体的稳定性和兼容性

4. **进度管理**：
   - 完成每个任务后，及时更新 improvement-plan.md 文档中的进度状态
   - 使用任务管理工具记录完成情况和遇到的问题
   - 在每个阶段完成后，使用 interactive_feedback_mcp-feedback-enhanced 工具向用户汇报进度并获取反馈

请先查看 improvement-plan.md 文档内容，了解当前的任务列表和优先级，然后开始执行第一个待完成的任务。
任务完成后，请用中文提供详细的工作总结。

**最终目标：** 确保全部完善，将项目从当前MVP状态提升为生产就绪的企业级应用，确保代码质量、用户体验和系统稳定性的全面提升。

------------------------------------------------------------------------------------------------------------------------

RPG
Project URL：https://zcigqzcjcmembbkxyuut.supabase.co
API Key：eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpjaWdxemNqY21lbWJia3h5dXV0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDIxNzksImV4cCI6MjA2Njg3ODE3OX0.xOJqIGCcATkFGO0Du1C7E0jR-_8sJu28EzELPMTfRlY

RPG2
Project URL：https://gscrmwhwknqulpiaoaaw.supabase.co
API Key：eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzY3Jtd2h3a25xdWxwaWFvYWF3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEzMDI1NTgsImV4cCI6MjA2Njg3ODU1OH0.yBTJC7R6WfCdf_iHKFupkJuLAWJms27_eGRH_ZBA5SQ

SHUQIAN
**Supabase连接配置：**
- Project URL: https://cfkmwfhsztohvhzeopzt.supabase.co
- API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNma213ZmhzenRvaHZoemVvcHp0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE1MDM2OTAsImV4cCI6MjA2NzA3OTY5MH0.HAkU9LvX3Yb57sgImN76zc739JHldumfBascKexA48s






这个是Chrome扩展插件，目前有个数据同步功能没完善，我想把这个数据同步重做成对接Supabase的，能够同步用户所有书签和设置，不需要迁移旧的同步数据到新的Supabase系统。
但是Chrome扩展插件的安全策略比较严格，可能需要把Supabase的库下载到本地，因为不允许连接外部资源，Supabase的运行库我已经提前下载放在src\lib目录了。
需要把原来旧的数据同步设置删除了，再把最新这个基于Supabase云端同步的功能，集成在扩展设置里面。旧的同步数据不需要迁移过去新的Supabase里面了。
Supabase的云端同步功能，实现书签数据的双向同步，本地更改立即同步到云端，云端更改实时推送到本地。
最后根据这个Supabase同步项目的内容和数据表结构，还需要给我一份在Suapabase运行的完整SQL命令脚本。
你修改完成之后不需要测试了，因为这个是Chrome扩展，你无法测试。但我希望功能都能正常使用。

 提醒一下同步Chrome的所有书签，请注意层级关系，Chrome书签结构是这样的：
  根节点 "0" (跳过)
  ├── 书签栏 "1" (跳过容器，但处理其子节点)
  │   ├── 用户书签1 (要同步)
  │   ├── 用户文件夹1 (要同步)
  │   └── ... (要同步)
  ├── 其他书签 "2" (跳过容器，但处理其子节点)
  └── 移动设备书签 "3" (跳过容器，但处理其子节点)

以下是Supabase的连接信息
Project URL：https://hfffjqxetwochxnbwufy.supabase.co
API Key：eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhmZmZqcXhldHdvY2h4bmJ3dWZ5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NDY0MjIsImV4cCI6MjA2NzIyMjQyMn0.61HG-Nr2SfSXuIRqW1FoQxzEsC16AJiDc2QIyk-qBhM





我需要为TabMark Chrome扩展重新设计数据同步功能，将其从现有的同步系统换为基于Supabase的云端同步解决方案。请按以下具体要求实施：

项目名称：TabMark Chrome扩展 - 数据同步系统重构
目标：将现有同步系统替换为基于Supabase的云端同步解决方案

**项目背景：**
- 这是一个Chrome浏览器扩展插件
- 现有的数据同步功能不完善，需要完全重做，不需要迁移旧的同步数据到新的Supabase系统
- 由于Chrome扩展的内容安全策略(CSP)限制，无法加载外部CDN资源，Supabase库已预先下载并放置在 `src/lib/` 目录中

**技术要求：**
1. **移除旧系统：** 完全删除现有的数据同步相关代码和设置
2. **Supabase集成：** 使用本地Supabase库文件，不依赖外部CDN
3. **数据同步范围：** 同步用户的所有书签数据和扩展设置
4. **实时双向同步**：本地更改立即同步到云端，云端更改实时推送到本地
5. **冲突解决**：处理多设备间的数据冲突
6. **用户界面：** 将新的云端同步功能集成到扩展的设置界面中

**Supabase连接配置：**
- Project URL: https://jwykrjzhfoufufliqcdf.supabase.co
- API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imp3eWtyanpoZm91ZnVmbGlxY2RmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2NTIyNzksImV4cCI6MjA2NzIyODI3OX0.PxVcZLf86dR5eMs2tpOGeshOzOMAdhAA1nqmbX4DP-s

**实施步骤建议：**
1. 分析并移除现有同步系统的所有组件和调用
2. 设计Supabase数据库表结构和RLS(行级安全)策略（用户认证、书签数据、设置数据）
3. 创建Supabase客户端连接模块（使用本地库Supabase文件）
4. 实现用户认证功能（注册/登录/登出）
5. 实现书签数据的双向同步（双向同步/上传/下载/冲突解决）
6. 实现设置数据的同步
7. 在设置界面中集成云端同步选项卡
8. 添加同步状态指示器和错误处理
9. 加入实时订阅

**注意事项：**
- 确保遵循Chrome扩展的安全策略
- 实现适当的错误处理和用户反馈
- 考虑数据隐私和安全性
- 提供清晰的用户界面和操作指引

 同步Chrome的所有书签，请注意层级关系，Chrome书签结构是这样的：
  根节点 "0" (跳过)
  ├── 书签栏 "1" (跳过容器，但处理其子节点)
  │   ├── 用户书签1 (要同步)
  │   ├── 用户文件夹1 (要同步)
  │   └── ... (要同步)
  ├── 其他书签 "2" (跳过容器，但处理其子节点)
  └── 移动设备书签 "3" (跳过容器，但处理其子节点)

请基于现有代码结构，提供完整的实现方案，包括具体的数据库设计和安全考虑。
请创建一个详细的任务列表（Task List）来组织和跟踪开发进度。
请注意：由于这是一个Chrome扩展项目，请不要尝试使用工具或测试工具来验证功能。Chrome扩展需要在浏览器扩展环境中运行，无法通过常规的测试方式进行验证。
请专注于代码实现，确保代码逻辑正确、语法无误，并遵循Chrome扩展的开发规范即可。






记账系统存在一个严重的月份状态管理BUG，具体表现如下：

**问题描述：**
1. 用户在6月份对部分记录进行了完成标记
2. 当自然月份从6月切换到7月时，系统自动切换到7月份视图
3. 此时出现错误的数据状态：
   - 7月份显示了6月份的完成标记（应该是空白状态，除非用户之前在7月份手动标记过）
   - 6月份的完成标记被错误地清空了（应该保持原有的完成状态）

**预期正确行为：**
- 6月份的完成标记应该永久保存，切换月份后再回到6月份时应该显示原有的完成状态
- 7月份作为新月份，应该显示所有记录的未完成状态（除非用户之前在7月份进行过标记）
- 每个月份的完成状态应该独立存储和管理，不应该相互覆盖

**需要修复的核心问题：**
月份切换时的状态管理逻辑错误，导致历史月份数据丢失和新月份数据错误继承。

请检查是否存在这个问题，并修复月份状态管理相关的代码逻辑。






我遇到了一个跨环境检测问题：

**环境配置：**
- WSL Ubuntu 系统中安装了 Claudia 应用和 Claude Code
- Claudia 应用通过 `npm run dev` 在 WSL 中启动，访问地址为 http://localhost:1420
- Claude Code 安装在 WSL Ubuntu 系统中

**问题描述：**
当 Claudia 应用在 Windows 环境中运行时（即使是从 WSL 启动），其检测逻辑只能识别 Windows 系统中安装的 Claude Code，无法检测到安装在 WSL Ubuntu 系统中的 Claude Code。

**技术原因：**
- Claudia 应用的后端实际运行在 Windows 环境中，即使通过 WSL 启动
- Claude Code 位于 WSL 环境中，存在环境隔离

**需要解决的问题：**
1. 如何让 Windows 环境中，打开http://localhost:1420 访问的 Claudia 检测 WSL 中的 Claude Code
2. 是否需要修改检测路径或检测方法
3. 如何处理 WSL 和 Windows 之间的路径映射问题

请分析当前的检测代码并提供解决方案。







--------------------------------------------------


请帮我开发一款全中文界面的提示词管理工具，具体要求如下：

**技术栈要求：**
- 开发环境：Devbox
- 后端：PHP
- 数据库：MySQL
- 前端：Tailwind CSS + DaisyUI 组件库
- 界面语言：中文简体

**功能需求：**
1. **提示词展示**：以卡片形式展示提示词列表
2. **详情查看**：点击卡片显示完整提示词内容
3. **一键复制**：每个提示词支持一键复制功能
4. **分类管理**：支持提示词分类组织和管理
5. **搜索功能**：支持按关键词搜索提示词
6. **编辑功能**：支持编辑现有提示词
7. **新增功能**：支持添加新的提示词
8. **统计显示**：提示词卡片显示该提示词使用次数

**数据库连接信息：**
- 数据库名：tishici-php
- 用户名：root
- 密码：9qdqn7qg
- 主机：tishici-php-mysql.ns-qqzrupud.svc
- 端口：3306

**部署信息：**
- 公网调试地址：https://bnpgznsbnmuu.sealosgzg.site

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7.请使用公网地址访问应用

请按照任务管理流程创建详细的开发计划，并逐步实现所有功能。


--------------------------------------------------


请帮我开发一款全中文界面的提示词管理工具，目录已经创建好React基础框架，具体要求如下：

**技术栈要求：**
- 开发环境：Devbox
- 后端：Node.js
- 数据库：PostgreSQL
- 前端：React + TypeScript + Tailwind CSS + DaisyUI 组件库
- 界面语言：中文简体

**功能需求：**
1. **提示词展示**：以卡片形式展示提示词列表
2. **详情查看**：点击卡片显示完整提示词内容
3. **一键复制**：每个提示词支持一键复制功能
4. **分类管理**：支持提示词分类组织和管理
5. **搜索功能**：支持按关键词模糊搜索提示词
6. **编辑功能**：支持编辑现有提示词
7. **新增功能**：支持添加新的提示词
8. **统计显示**：提示词卡片显示该提示词使用次数

**数据库连接信息：**
- 数据库名：tishici-react
- 用户名：postgres
- 密码：9xkslw9n
- 主机：tishici-react-postgresql.ns-qqzrupud.svc
- 端口：5432

**部署信息：**
- 公网调试地址：https://ookctyjexeuq.sealosgzg.site

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7.请使用公网地址访问应用

请按照任务管理流程创建详细的开发计划，并逐步实现所有功能。


--------------------------------------------------


请帮我开发一款全中文界面的提示词管理工具，具体要求如下：

**技术栈要求：**
- 开发环境：Devbox
- 后端：Go语言
- 数据库：MySQL
- 前端：Tailwind CSS + DaisyUI 组件库
- 界面语言：中文简体

**功能需求：**
1. **提示词展示**：以卡片形式展示提示词列表
2. **详情查看**：点击卡片显示完整提示词内容
3. **一键复制**：每个提示词支持一键复制功能
4. **分类管理**：支持提示词分类组织和管理
5. **搜索功能**：支持按关键词模糊搜索提示词
6. **编辑功能**：支持编辑现有提示词
7. **新增功能**：支持添加新的提示词
8. **统计显示**：提示词卡片显示该提示词使用次数

**数据库连接信息：**
- 数据库名：tishici-go
- 用户名：root
- 密码：7f7r2tnh
- 主机：tishici-go-mysql.ns-qqzrupud.svc
- 端口：3306

**部署信息：**
- 公网调试地址：https://toafwrdcjttk.sealosgzg.site

**开发要求：**
1. 创建完整的数据库表结构
2. 实现响应式设计，适配移动端和桌面端
3. 使用现代化的UI设计，界面美观易用
4. 代码结构清晰，遵循最佳实践
5. 包含错误处理和数据验证
6. 提供完整的CRUD操作功能
7.请使用公网地址访问应用

请按照任务管理流程创建详细的开发计划，并逐步实现所有功能。












请你根据项目根目录下的 `prompt.md` 文档内容，制定一个详细的开发规划和执行计划。具体要求如下：

1. **文档分析**：
   - 仔细阅读并分析 `prompt.md` 文档中的所有需求和规格说明
   - 识别项目的核心功能、技术栈要求和业务逻辑

2. **数据库配置确认**：
   - 检查 `Supabase.md` 文件中的 Supabase 连接配置信息
   - 已执行的 SQL 脚本：`supabase-rls-policies.sql`（行级安全策略）和 `supabase-schema.sql`（数据库架构）
   - 如果需要额外的数据库表、字段或 RLS 策略，请在规划中明确指出并准备相应的 SQL 补充脚本

3. **项目基础**：
   - 利用 `prompt` 目录下已准备好的 Create T3 App 模板文件
   - 确认模板的完整性和可用性

4. **开发规划要求**：
   - 创建详细的任务清单，包含具体的开发步骤和优先级
   - 同步创建 `todos.md` 文档记录任务进度
   - 同步创建 `execution-log.md` 文档用于实时记录执行过程
   - 按功能模块划分开发阶段
   - 明确技术实现方案、文件结构和代码架构

5. **技术考虑**：
   - 确保与现有的 T3 App 技术栈兼容
   - 考虑 WSL 环境下的开发和部署要求
   - 包含必要的测试策略和验证方法

6. **输出格式**：
   - 提供结构化的开发计划文档
   - 包含时间估算和优先级排序
   - 明确每个阶段的交付物和验收标准

请先使用工具查看 `prompt.md` 文件内容，然后基于其内容制定完整的开发详细规划。