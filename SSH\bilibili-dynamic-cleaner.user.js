// ==UserScript==
// @name         B站动态批量删除器
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  批量删除B站个人动态，支持暂停/继续/停止
// <AUTHOR>
// @match        https://space.bilibili.com/*/dynamic
// @match        https://www.bilibili.com/opus/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 全局状态管理
    let isRunning = false;
    let isPaused = false;
    let shouldStop = false;
    let currentIndex = 0;
    let totalProcessed = 0;
    let currentData = [];

    // 工具函数
    const sleep = (seconds) => new Promise(resolve => setTimeout(resolve, seconds * 1000));
    
    // 日志函数
    const log = (message, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] 动态清理器:`;
        
        switch(type) {
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: #4CAF50; font-weight: bold;');
                break;
            case 'warning':
                console.log(`%c${prefix} ${message}`, 'color: #FF9800; font-weight: bold;');
                break;
            case 'error':
                console.log(`%c${prefix} ${message}`, 'color: #F44336; font-weight: bold;');
                break;
            default:
                console.log(`%c${prefix} ${message}`, 'color: #2196F3; font-weight: bold;');
        }
        
        updateStatusDisplay(message, type);
    };

    // 创建控制面板UI
    function createControlPanel() {
        const panel = document.createElement('div');
        panel.id = 'bili-cleaner-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                width: 300px;
                background: #fff;
                border: 2px solid #00a1d6;
                border-radius: 10px;
                padding: 15px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
            ">
                <div style="
                    color: #00a1d6;
                    font-weight: bold;
                    font-size: 16px;
                    margin-bottom: 15px;
                    text-align: center;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                ">
                    🧹 B站动态清理器
                </div>
                
                <div id="cleaner-status" style="
                    background: #f5f5f5;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 15px;
                    min-height: 40px;
                    font-size: 12px;
                    color: #666;
                ">
                    就绪状态 - 点击开始按钮启动清理
                </div>
                
                <div id="cleaner-stats" style="
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 10px;
                    margin-bottom: 15px;
                    font-size: 12px;
                ">
                    <div>已处理: <span id="processed-count">0</span></div>
                    <div>剩余: <span id="remaining-count">-</span></div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button id="start-btn" style="
                        background: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 12px;
                    ">开始清理</button>
                    
                    <button id="pause-btn" style="
                        background: #FF9800;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 12px;
                    " disabled>暂停</button>
                </div>
                
                <div style="margin-top: 10px; display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <button id="stop-btn" style="
                        background: #F44336;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 12px;
                    " disabled>停止</button>
                    
                    <button id="close-btn" style="
                        background: #666;
                        color: white;
                        border: none;
                        padding: 8px 12px;
                        border-radius: 5px;
                        cursor: pointer;
                        font-size: 12px;
                    ">关闭面板</button>
                </div>
                
                <div style="
                    margin-top: 15px;
                    padding-top: 10px;
                    border-top: 1px solid #eee;
                    font-size: 11px;
                    color: #999;
                    text-align: center;
                ">
                    ⚠️ 删除操作不可恢复，请谨慎使用
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        bindPanelEvents();
    }

    // 绑定面板事件
    function bindPanelEvents() {
        document.getElementById('start-btn').addEventListener('click', startCleaning);
        document.getElementById('pause-btn').addEventListener('click', togglePause);
        document.getElementById('stop-btn').addEventListener('click', stopCleaning);
        document.getElementById('close-btn').addEventListener('click', closePanel);
    }

    // 更新状态显示
    function updateStatusDisplay(message, type) {
        const statusEl = document.getElementById('cleaner-status');
        const processedEl = document.getElementById('processed-count');
        const remainingEl = document.getElementById('remaining-count');
        
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.style.color = type === 'error' ? '#F44336' : 
                                  type === 'success' ? '#4CAF50' : 
                                  type === 'warning' ? '#FF9800' : '#666';
        }
        
        if (processedEl) processedEl.textContent = totalProcessed;
        if (remainingEl) remainingEl.textContent = currentData.length - currentIndex;
    }

    // 更新按钮状态
    function updateButtonStates() {
        const startBtn = document.getElementById('start-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const stopBtn = document.getElementById('stop-btn');
        
        if (startBtn) startBtn.disabled = isRunning;
        if (pauseBtn) {
            pauseBtn.disabled = !isRunning;
            pauseBtn.textContent = isPaused ? '继续' : '暂停';
        }
        if (stopBtn) stopBtn.disabled = !isRunning;
    }

    // 获取动态列表
    function getDynamicElements() {
        return document.querySelectorAll('.tp.bili-dyn-more__btn');
    }

    // 删除单个动态
    async function deleteSingleDynamic(element, index) {
        try {
            log(`正在删除第 ${totalProcessed + 1} 个动态...`);
            
            // 触发悬停事件
            const hoverEvent = new MouseEvent('mouseenter', { bubbles: true });
            element.dispatchEvent(hoverEvent);
            await sleep(0.5);

            // 点击删除选项
            const deleteOption = document.querySelectorAll('.bili-cascader-options__item-custom')[1];
            if (!deleteOption) {
                throw new Error('未找到删除选项');
            }
            deleteOption.click();
            await sleep(0.5);

            // 确认删除
            const confirmBtn = document.querySelector('.bili-modal__button.confirm.red');
            if (!confirmBtn) {
                throw new Error('未找到确认按钮');
            }
            confirmBtn.click();
            await sleep(0.5);

            totalProcessed++;
            log(`已删除第 ${totalProcessed} 个动态`, 'success');
            
            return true;
        } catch (error) {
            log(`删除动态时出错: ${error.message}`, 'error');
            return false;
        }
    }

    // 滚动加载更多
    async function loadMoreDynamics() {
        log('正在加载更多动态...');
        window.scrollBy(0, 100000);
        await sleep(1);
        
        const newData = getDynamicElements();
        log(`重新获取，当前数量: ${newData.length}`);
        
        window.scrollBy(0, -100000);
        await sleep(0.5);
        
        return newData;
    }

    // 主清理函数
    async function startCleaning() {
        if (isRunning) return;
        
        // 确认操作
        if (!confirm('确定要开始批量删除动态吗？此操作不可恢复！')) {
            return;
        }
        
        isRunning = true;
        isPaused = false;
        shouldStop = false;
        currentIndex = 0;
        totalProcessed = 0;
        
        updateButtonStates();
        log('开始批量删除动态...');
        
        try {
            currentData = getDynamicElements();
            log(`发现 ${currentData.length} 个动态`);
            
            while (currentIndex < currentData.length && !shouldStop) {
                // 检查暂停状态
                while (isPaused && !shouldStop) {
                    await sleep(0.1);
                }
                
                if (shouldStop) break;
                
                const success = await deleteSingleDynamic(currentData[currentIndex], currentIndex);
                
                if (success) {
                    // 检查是否需要加载更多
                    if (currentData.length - currentIndex < 5) {
                        const newData = await loadMoreDynamics();
                        if (newData.length > 0) {
                            currentData = newData;
                            currentIndex = 0;
                            continue;
                        } else {
                            log('所有动态已清理完成！', 'success');
                            break;
                        }
                    }
                }
                
                currentIndex++;
            }
            
        } catch (error) {
            log(`清理过程出错: ${error.message}`, 'error');
        } finally {
            isRunning = false;
            isPaused = false;
            updateButtonStates();
            
            if (shouldStop) {
                log('清理已手动停止', 'warning');
            } else {
                log(`清理完成！共处理 ${totalProcessed} 个动态`, 'success');
            }
        }
    }

    // 暂停/继续
    function togglePause() {
        if (!isRunning) return;
        
        isPaused = !isPaused;
        updateButtonStates();
        log(isPaused ? '已暂停清理' : '继续清理中...', 'warning');
    }

    // 停止清理
    function stopCleaning() {
        if (!isRunning) return;
        
        shouldStop = true;
        isPaused = false;
        log('正在停止清理...', 'warning');
    }

    // 关闭面板
    function closePanel() {
        if (isRunning && !confirm('清理正在进行中，确定要关闭面板吗？')) {
            return;
        }
        
        const panel = document.getElementById('bili-cleaner-panel');
        if (panel) {
            panel.remove();
        }
    }

    // 初始化
    function init() {
        // 检查页面类型
        if (!window.location.href.includes('space.bilibili.com') && 
            !window.location.href.includes('bilibili.com/opus')) {
            return;
        }
        
        // 等待页面加载完成
        setTimeout(() => {
            const dynamicElements = getDynamicElements();
            if (dynamicElements.length > 0) {
                createControlPanel();
                log(`检测到 ${dynamicElements.length} 个动态，清理器已就绪`);
            } else {
                log('未检测到动态内容，请确认当前页面是否为动态页面', 'warning');
            }
        }, 2000);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})(); 