# Supabase国内加速配置指南

## 🚀 方案对比

| 方案 | 加速效果 | 配置难度 | 成本 | 推荐度 |
|------|----------|----------|------|--------|
| Cloudflare Workers | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 免费 | 🔥推荐 |
| 七牛云CDN | ⭐⭐⭐⭐ | ⭐⭐ | 低 | 推荐 |
| 阿里云CDN | ⭐⭐⭐⭐ | ⭐⭐ | 低 | 推荐 |
| 客户端缓存 | ⭐⭐⭐ | ⭐ | 免费 | 基础 |

## 📋 具体实施步骤

### 1. Cloudflare Workers代理（推荐）

#### 步骤1：创建Worker
1. 访问 [Cloudflare Workers](https://workers.cloudflare.com/)
2. 注册/登录账号
3. 创建新的Worker
4. 复制 `supabase-proxy-worker.js` 的代码
5. 修改其中的 `SUPABASE_URL` 为您的项目URL

#### 步骤2：部署Worker
```bash
# 使用Wrangler CLI部署
npm install -g wrangler
wrangler login
wrangler publish
```

#### 步骤3：配置自定义域名（可选）
```bash
# 添加自定义域名
wrangler route add "api.yourdomain.com/*" your-worker-name
```

### 2. 七牛云CDN配置

#### 创建加速域名
```javascript
// 七牛云CDN配置示例
const QINIU_CDN_URL = 'https://your-domain.qiniucdn.com'

// 配置Supabase使用七牛云代理
const supabase = createClient(QINIU_CDN_URL, SUPABASE_ANON_KEY, {
  global: {
    headers: {
      'X-Original-Host': 'your-project.supabase.co'
    }
  }
})
```

### 3. 阿里云CDN配置

#### 回源设置
```nginx
# 阿里云CDN回源配置
upstream supabase_backend {
    server your-project.supabase.co:443;
}

server {
    listen 443 ssl;
    server_name your-cdn-domain.com;
    
    location / {
        proxy_pass https://supabase_backend;
        proxy_set_header Host your-project.supabase.co;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🔧 客户端优化配置

### 使用优化的Supabase配置
```javascript
import { supabase, cachedQuery, checkConnection } from './supabase-config-optimized.js'

// 检查连接状态
const connectionStatus = await checkConnection()
console.log('连接状态:', connectionStatus)

// 使用缓存查询
const { data, error } = await cachedQuery('posts', {
  limit: 10,
  order: { column: 'created_at', ascending: false }
})
```

### 批量请求优化
```javascript
// 批量执行多个查询
const queries = [
  { table: 'posts', query: { limit: 5 } },
  { table: 'users', query: { limit: 10 } },
  { table: 'categories', query: {} }
]

const results = await batchQuery(queries)
```

## 📊 性能监控

### 延迟测试
```javascript
// 测试不同地区的延迟
async function testLatency() {
  const regions = {
    'direct': 'https://your-project.supabase.co',
    'cloudflare': 'https://your-worker.workers.dev',
    'qiniu': 'https://your-domain.qiniucdn.com'
  }
  
  for (const [name, url] of Object.entries(regions)) {
    const start = Date.now()
    try {
      await fetch(`${url}/rest/v1/`, { method: 'HEAD' })
      const latency = Date.now() - start
      console.log(`${name}: ${latency}ms`)
    } catch (error) {
      console.log(`${name}: 失败 - ${error.message}`)
    }
  }
}

testLatency()
```

## 🛠️ 故障排除

### 常见问题

1. **CORS错误**
   - 确保代理服务器正确设置了CORS头
   - 检查API密钥是否正确

2. **认证失败**
   - 验证代理是否正确转发Authorization头
   - 检查JWT令牌是否有效

3. **超时问题**
   - 增加客户端超时时间
   - 使用连接池优化

### 调试命令
```bash
# 测试直连延迟
curl -w "@curl-format.txt" -o /dev/null -s "https://your-project.supabase.co/rest/v1/"

# 测试代理延迟
curl -w "@curl-format.txt" -o /dev/null -s "https://your-worker.workers.dev/rest/v1/"
```

## 🔍 推荐组合方案

### 方案A：成本优先
- Cloudflare Workers（免费）
- 客户端缓存优化
- 预估加速：60-80%

### 方案B：性能优先
- Cloudflare Workers + 自定义域名
- 七牛云/阿里云CDN双重加速
- 智能缓存策略
- 预估加速：80-95%

### 方案C：企业级
- 多地区CDN部署
- 数据库读写分离
- Redis缓存层
- 预估加速：95%+

## 📞 技术支持

如果遇到配置问题，可以：
1. 检查网络连接状态
2. 验证API密钥和URL配置
3. 查看浏览器开发者工具的网络面板
4. 使用提供的调试工具进行排查 