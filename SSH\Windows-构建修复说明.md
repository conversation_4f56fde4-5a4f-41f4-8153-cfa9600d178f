# Windows 构建问题修复指南

## 🚨 问题描述

在 Windows 系统下运行 `bun run tauri dev` 时出现以下错误：

```bash
Running: cp -r D:\Cursor Project\Tools\claudia\temp-claude-package\package\vendor D:\Cursor Project\Tools\claudia\vendor
'cp' is not recognized as an internal or external command,
operable program or batch file.
```

**根本原因**: 构建脚本使用了 Unix/Linux 的 `cp` 命令，Windows PowerShell 不支持此命令。

## 🔧 解决方案

### 方案1：手动修复（推荐）

1. **运行构建命令直到出现错误**：
   ```powershell
   cd "D:\Cursor Project\Tools\claudia"
   bun run tauri dev
   ```

2. **当出现 cp 错误时，停止构建**

3. **复制修复脚本到项目目录**：
   将以下文件复制到 `D:\Cursor Project\Tools\claudia\` 目录：
   - `copy-claude-files.bat`
   - `copy-claude-files.ps1`
   - `fix-windows-build.js`

4. **运行修复脚本**：
   ```powershell
   # 方式1: 使用批处理脚本
   .\copy-claude-files.bat
   
   # 方式2: 使用 PowerShell 脚本
   .\copy-claude-files.ps1
   
   # 方式3: 使用 Node.js 脚本
   node fix-windows-build.js
   ```

5. **继续构建过程**：
   ```powershell
   bun run tauri dev
   ```

### 方案2：永久修复（需要修改源码）

修改项目中的 `scripts/fetch-and-build.js` 文件，将 Unix 的 `cp` 命令替换为跨平台兼容的 Node.js 文件操作。

#### 示例修改：

**原代码（有问题）**：
```javascript
execSync(`cp -r ${sourcePath} ${destPath}`);
```

**修复后的代码**：
```javascript
const fs = require('fs-extra');
await fs.copy(sourcePath, destPath);
```

## 📋 文件说明

### copy-claude-files.bat
Windows 批处理脚本，使用 `robocopy` 和 `copy` 命令复制文件。

### copy-claude-files.ps1
PowerShell 脚本，使用 `Copy-Item` cmdlet 复制文件。

### fix-windows-build.js
Node.js 脚本，提供跨平台文件复制功能，并自动生成上述两个脚本。

## 🔍 详细步骤演示

### 1. 识别错误

当你看到类似以下错误时：
```
Running: cp -r /path/to/source /path/to/dest
'cp' is not recognized as an internal or external command
```

### 2. 使用修复脚本

在项目根目录（`D:\Cursor Project\Tools\claudia\`）运行：

```powershell
# 检查临时文件是否存在
ls temp-claude-package\package\

# 运行修复脚本
.\copy-claude-files.bat
```

### 3. 验证修复

检查文件是否已复制：
```powershell
# 检查 vendor 目录
ls vendor\

# 检查其他文件
ls cli.js
ls yoga.wasm
```

## ⚠️ 注意事项

1. **路径问题**: 确保在正确的项目目录下运行脚本
2. **权限问题**: 可能需要管理员权限运行 PowerShell
3. **临时文件**: 构建过程会产生临时文件，脚本会自动处理
4. **错误处理**: 如果 `robocopy` 失败，脚本会自动尝试 `xcopy`

## 🚀 自动化解决方案

为了避免每次都手动修复，可以：

1. **修改 package.json**，在构建前自动运行修复脚本
2. **创建 Windows 专用的构建脚本**
3. **联系项目维护者**，提供跨平台兼容性补丁

## 📞 获取帮助

如果问题仍然存在，请：

1. 检查 Windows 版本和 PowerShell 版本
2. 确认 `robocopy` 和 `xcopy` 命令可用
3. 查看详细的错误日志
4. 考虑在 WSL（Windows Subsystem for Linux）中运行构建

## 🎉 预期结果

修复成功后，你应该能够：

- 完成 `bun run tauri dev` 命令
- 看到 Tauri 开发环境启动
- 正常使用 Claudia 应用程序

---

**修复状态**: ✅ 已创建修复脚本，可立即使用 