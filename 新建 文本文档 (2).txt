目前是Windows本地的开发环境，我想你根据这个项目开发说明文档，帮我开发这个个人知识管理平台，请严格按照以上要求开始开发，请你认真阅读此项目开发说明，把这个@项目开发说明.md 文档记住。
开发之前请先制定和创建详细合理的开发计划文档和开发进度文档（Markdown文档），然后按照开发计划文档进行有序开发，并且每次开发过程都需要更新这个开发进度文档。
CDN：如果开发需要用到引入CDN，请使用国内能访问的CDN资源。
最后：开发完毕请根据项目开发说明文档，前后端逐一检查各个模块是否完整实现，所有的模块、功能和组件是否都已经完善，是否都已经能正常工作。
注意：不需要进行测试，开发完毕之后我会上传到服务器部署自己进行测试，请你记住以上所有内容。

按照你计划和项目开发说明，完成项目开发说明文档里面的所有的模块、功能和组件。

我看你一开始检查项目目录的时候好像是错的，你检查了当前项目在：C:\Users\<USER>\Desktop\SSH，但事实上这个项目的目录在：D:\Cursor Project\website\workspace。
我不知道这个有没有影响你后续的开发，因为我的疏忽一直没有纠正过来。
然后我看到现在项目的目录结构跟项目开发说明文档里面的目录结构完全不一样，我不知道整个开发有没有问题，功能有没有问题。
所以我想你认真检查一下。




想开发一个类似WordPress但技术更先进，高性能、轻量级的CMS后端系统。目标是开发一个技术先进、高性能、轻量级的类WordPress CMS后端，摒弃其历史包袱。
考虑到CMS系统的复杂性，我会建议分层设计。核心系统要轻量，通过插件机制扩展功能。数据库方面需要权衡灵活性和性能，NoSQL适合内容模型但关系型更成熟。API优先设计很重要，这样未来可以轻松支持各种前端。
技术栈偏向使用：Node.js (NestJS) + TypeScript + PostgreSQL (JSONB) + GraphQL + Redis + React 前端，这样的组合是目前平衡现代化、性能、开发效率和生态的一个非常强大的选择，尤其适合打造一个面向未来的 Headless CMS。

1、采用Headless架构（API-First），核心引擎保持轻量、稳定。
2、前端分离 (Headless CMS)，核心理念：后端只通过API（REST/GraphQL）提供数据和内容管理功能
3、所有非核心功能（自定义内容类型、工作流、SEO工具、表单、电商等）都通过插件/模块方式添加，设计清晰的插件接口
4、先构建一个稳定、灵活、可扩展的核心引擎（用户、权限、基础内容模型、API、插件系统），其他功能通过插件实现
5、前端（React, Vue, Angular, Next.js, Nuxt.js, SvelteKit, 移动App等）完全独立开发、部署
6、插件化架构 (Microkernel)：核心引擎轻量稳定，所有非核心功能通过插件机制扩展。Hook/Event 系统是灵魂。
7、事件驱动架构，采用事件驱动架构，核心服务通过事件总线发布关键操作事件（如内容创建、发布），插件或其他服务监听这些事件并作出响应，实现高度解耦。

### 核心模块划分
- 用户与认证模块
- 内容核心引擎 (Content Engine) 
- 媒体管理模块
- 搜索模块
- 插件/扩展模块
- 设置与配置模块
- 任务队列模块


开发一个类似WordPress但技术更先进，高性能、轻量化的CMS后端系统。
考虑到CMS系统的复杂性，建议采用分层设计。核心系统要轻量，通过插件机制扩展功能。
数据库方面需权衡灵活性和性能：PostgreSQL 的 JSONB 类型在成熟的关系型能力与 NoSQL 式灵活性之间取得了最佳平衡，是推荐方案。API优先设计是基础，确保未来无缝支持多终端前端。
技术栈偏向使用：Node.js (NestJS) + TypeScript + PostgreSQL (JSONB) + GraphQL + Redis + React (管理界面)，该组合在现代化、性能、开发效率与生态间达到理想平衡，是打造面向未来的 Headless CMS 的强力选择。

核心设计原则：
Headless 架构 (API-First)：核心引擎轻量稳定，仅通过 REST/GraphQL API 提供数据与服务。
前后端完全分离：前端（如 React/Vue 管理后台、Next.js/Nuxt.js 网站、移动 App）独立开发部署，与后端解耦。
插件化扩展 (Microkernel)：所有非核心功能（内容类型、SEO、表单、电商等）均通过插件实现，需设计标准化的插件接口与事件钩子 (Hooks)。
渐进式构建：优先实现可扩展的核心引擎（用户/权限/内容模型/API/插件系统），非核心功能后续通过插件添加。
事件驱动架构：核心服务通过事件总线发布关键事件（如内容发布、用户注册），插件或内部服务监听并响应，实现解耦。
高性能基石：异步队列处理耗时任务（如图片转码），Redis 缓存热点数据，Elasticsearch/OpenSearch 提供专业全文搜索。

核心模块划分
用户与认证模块：RBAC/ABAC 权限控制、OAuth/JWT 集成
内容核心引擎：灵活内容建模、JSONB 存储、版本控制、工作流
媒体管理模块：对接对象存储（S3/MinIO）、异步处理队列
搜索模块（基于 Elasticsearch/OpenSearch）：全文检索、高亮、聚合
插件/扩展模块：生命周期管理、Hook/Event 系统、沙箱安全隔离（可选）
设置与配置模块：全局配置、多站点支持
任务队列模块：保障核心 API 响应速度，解耦耗时操作

请你记住公网调试地址：https://pxkodgbazydx.sealosgzg.site
以后需要调试直接打开公网调试就可以了。
准备好的话，现在开始按照计划进行开发吧！！！




还有很多问题：
1、仪表盘点击“快速上传”没有反应
2、图片管理点击“上传图片”没有反应，点击“新建分类”没有反应
3、右上角个人中心点击“设置没有反应”
4、文档管理点击“新建文件夹”，“上传文档”没有反应
5、文档管理，复选管理文档“删除”，“移动到”，“批量下载”这些按钮都没有反应
6、文档管理“拖拽文档到此处上传”没有反应，“点击选择文件”没有反应
7、文档管理无法打开文档查阅和编辑
8、提示词库也有很多功能无法使用





我需要开发一个Linux+Nginx+PHP+MYSQL的网站，主要用于存放图片、文档、还有一些提示词信息。

以下是要求：
1、网站UI/UX风格现代化，且用户交互体验好，所有功能都直观方便使用
2、左边是工具栏，右边是功能区域，顶部是信息区域
3、移动端和桌面端分开样式，能够识别桌面端和移动端使用不同的样式，确保移动端有良好的触控体验
4、确保代码符合W3C标准，无错误警告
5、使用TailwindCSS 4.X+（通过国内CDN引入）
6、使用专业图标库如Font Awesome或Material Icons（通过国内CDN引入
7、确保页面加载速度快，避免不必要的大型资源（不要国外资源，因为是国内网站）
8、MYSQ数据库是云端数据库

图片：
1、能够提供上传和下载图片
2、图片以平铺方式展示（类似相册一样）

文档：
1、能够新增和删除文档
2、能够在线编辑文档

提示词：
1、能够新增分类目录、新增提示词
2、点击提示词能够打开整个提示词显示全部内容

目前已经是开发环境，详细版本信息：
PHP版本：8.4.6
已安装扩展
Core=8.4.6
date=8.4.6
libxml=8.4.6
openssl=8.4.6
pcre=8.4.6
sqlite3=8.4.6
zlib=8.4.6
ctype=8.4.6
curl=8.4.6
dom=20031129
fileinfo=8.4.6
filter=8.4.6
hash=8.4.6
iconv=8.4.6
json=8.4.6
mbstring=8.4.6
SPL=8.4.6
session=8.4.6
PDO=8.4.6
pdo_sqlite=8.4.6
standard=8.4.6
posix=8.4.6
random=8.4.6
readline=8.4.6
Reflection=8.4.6
Phar=8.4.6
SimpleXML=8.4.6
tokenizer=8.4.6
xml=8.4.6
xmlreader=8.4.6
xmlwriter=8.4.6
mysqlnd=mysqlnd 8.4.6
cgi-fcgi=8.4.6
bcmath=8.4.6
ftp=8.4.6
gd=8.4.6
gettext=8.4.6
intl=8.4.6
mysqli=8.4.6
pcntl=8.4.6
pdo_mysql=8.4.6
redis=6.2.0
shmop=8.4.6
soap=8.4.6
sockets=8.4.6
sodium=8.4.6
sysvsem=8.4.6
xmlrpc=1.0.0RC3
zip=1.22.5

云端MYSQL版本8.0，连接信息：
数据库名称：sync
数据库连接地址：1Panel-mysql-65pI
端口：3306
用户名：sync
密码：Abc112211

请你记住，以后调试都直接使用这个公网地址：https://www.shyfpay.com/



请用 HTML，CSS（使用 Tailwind CSS）和 JavaScript 创建一个登录表单：
 - 包含邮箱输入框（要求必填且格式为邮箱）
 - 包含密码输入框（要求必填，最少6位）
 - 提交按钮
 - 当用户点击提交时，进行前端验证：
   * 邮箱格式不对（没有@）显示错误提示“请输入有效的邮箱地址”
   * 密码为空或少于6位显示错误提示“密码不能少于6位”
   * 所有验证通过则 alert('提交成功！')
在输入框旁边显示错误提示信息。


在之前创建的登录表单基础上，请增加以下功能：
1. 实时邮箱验证：用户在邮箱输入框输入时（使用 `input` 事件），实时检查是否包含 '@'。如果格式无效，立即在下方显示红色文字提示“请输入有效的邮箱地址”，格式有效则清除提示。
2. 添加“确认密码”输入框（必填）。在提交表单时，检查“密码”和“确认密码”两个字段的值是否严格相等。如果不相等，在“确认密码”输入框下方显示红色错误提示“两次输入的密码不一致”。
3. 优化错误提示的样式（例如红色文字，更明显）。


1. 在之前的登录表单基础上，请增加邮箱唯一性检查：
2. 当用户填写完邮箱并移开焦点（`blur`事件）时：
    * 如果邮箱格式有效，则：
    - 在邮箱输入框下方显示提示“检查中...”（比如灰色文字）。
    - 使用 Axios 或 Fetch API 发送 GET 请求到 `/api/check-email?email=用户输入的邮箱`。
    - 预期后端返回 JSON 格式：`{ available: true }` 或 `{ available: false }`。
2. 根据 API 响应：
    * `available: true`：将提示文字变为绿色“邮箱可用！”。
    * `available: false`：将提示文字变为红色“该邮箱已被注册”。
3. 提交表单时，除了之前的验证规则，还必须检查邮箱的 `available`状态为 `true`。如果已被注册，阻止提交并提示用户。
4. 处理网络请求可能出现的错误（例如显示“检查失败，请重试”）。
请使用 Axios 或 Fetch 实现 API 调用部分。


------------------------------------------------------
------------------------------------------------------

# 代码块
请用 HTML、CSS（使用 Tailwind CSS）和 JavaScript 创建一个登录表单：
 - 包含邮箱输入框（要求必填且格式为邮箱）
 - 包含密码输入框（要求必填，最少6位）
 - 提交按钮
 - 当用户点击提交时，进行前端验证：
   * 邮箱格式不对（没有@）显示错误提示"请输入有效的邮箱地址"
   * 密码为空或少于6位显示错误提示"密码不能少于6位"
   * 所有验证通过则 `alert('提交成功！')`
在输入框旁边显示错误提示信息。


# 代码块
在之前创建的登录表单基础上，请增加以下功能：
 1. 实时邮箱验证：用户在邮箱输入框输入时（使用 `input` 事件），实时检查是否包含 `@`，如果格式无效，立即在下方显示红色文字提示"请输入有效的邮箱地址"；格式有效则清除提示。
 2. 添加"确认密码"输入框（必填）。在提交表单时，检查"密码"和"确认密码"两个字段的值是否严格相等。如果不相等，在"确认密码"输入框下方显示红色错误提示"两次输入的密码不一致"。
 3. 优化错误提示的样式（例如红色文字，更明显）。


# 代码块
在之前的登录表单基础上，请增加邮箱唯一性检查：
 1. 当用户填写完邮箱并移开焦点（`blur` 事件）时：
    * 如果邮箱格式有效，则：
      - 在邮箱输入框下方显示提示"检查中..."（比如灰色文字）。
      - 使用 Axios 或 Fetch API 发送 GET 请求到 `/api/check-email?email=用户输入的邮箱`。
      - 预期后端返回 JSON 格式：`{ available: true }` 或 `{ available: false }`。
 2. 根据 API 响应：
    * `available: true`：将提示文字变为绿色"邮箱可用！"。
    * `available: false`：将提示文字变为红色"该邮箱已被注册"。
 3. 提交表单时，除了之前的验证规则，还必须检查邮箱的 `available` 状态为 `true`，如果已被注册，阻止提交并提示用户。
 4. 处理网络请求可能出现的错误（例如显示"检查失败，请重试"）。
请使用 Axios 或 Fetch 实现 API 调用部分。

------------------------------------------------------
------------------------------------------------------

@/home/<USER>/project/README.md@/home/<USER>/project/docs/
根据这些项目文件，帮我全盘检查代码，不是只看文档，要真实检查代码。
1、每份文档都一个一个字去查阅。
2、每个代码都要一个一个去看清楚。
3、检查有哪些功能有缺失、
4、检查有哪些地方有冲突。
5、检查是否按照项目标准去开发。
6、检查所有代码的逻辑是否正确。
7、检查所有代码的关联性是有正确。
8、检查所有代码的交互是否有问题。
9、了解项目现在做到什么进度了。
10、总结一下所有情况制作成MD文档。

一共10个要点，都要全部认真仔细去做。

我原来的要求是这样的：
“根据所有项目文档，开发一个类似WordPress但技术更先进，高性能、轻量化的CMS后端系统。 
考虑到CMS系统的复杂性，建议采用分层设计。核心系统要轻量，通过插件机制扩展功能。 
数据库方面需权衡灵活性和性能：PostgreSQL 的 JSONB 类型在成熟的关系型能力与 NoSQL 式灵活性之间取得了最佳平衡，是推荐方案。API优先设计是基础，确保未来无缝支持多终端前端。 
技术栈偏向使用：Node.js (NestJS) + TypeScript + PostgreSQL (JSONB) + GraphQL + Redis + React (管理界面)，该组合在现代化、性能、开发效率与生态间达到理想平衡，是打造面向未来的 Headless CMS 的强力选择。 
 
核心设计原则： 
Headless 架构 (API-First)：核心引擎轻量稳定，仅通过 REST/GraphQL API 提供数据与服务。 
前后端完全分离：前端（如 React/Vue 管理后台、Next.js/Nuxt.js 网站、移动 App）独立开发部署，与后端解耦。 
插件化扩展 (Microkernel)：所有非核心功能（内容类型、SEO、表单、电商等）均通过插件实现，需设计标准化的插件接口与事件钩子 (Hooks)。 
渐进式构建：优先实现可扩展的核心引擎（用户/权限/内容模型/API/插件系统），非核心功能后续通过插件添加。 
事件驱动架构：核心服务通过事件总线发布关键事件（如内容发布、用户注册），插件或内部服务监听并响应，实现解耦。 
高性能基石：异步队列处理耗时任务（如图片转码），Redis 缓存热点数据，Elasticsearch/OpenSearch 提供专业全文搜索。 
 
核心模块划分 
用户与认证模块：RBAC/ABAC 权限控制、OAuth/JWT 集成 
内容核心引擎：灵活内容建模、JSONB 存储、版本控制、工作流 
媒体管理模块：对接对象存储（S3/MinIO）、异步处理队列 
搜索模块（基于 Elasticsearch/OpenSearch）：全文检索、高亮、聚合 
插件/扩展模块：生命周期管理、Hook/Event 系统、沙箱安全隔离（可选） 
设置与配置模块：全局配置、多站点支持 
任务队列模块：保障核心 API 响应速度，解耦耗时操作 
 
请你记住公网调试地址：https://pxkodgbazydx.sealosgzg.site 
以后需要调试直接打开公网调试就可以了。”








我想你帮我制作一个纯静态的优秀收录网址的导航，收录的网址能够满足所有人的需求。（想要的网站在这个导航网址就能找到的）
1、使用HTML5、TailwindCSS 4.x+（通过国内CDN引入）和必要的JavaScript 
2、使用专业图标库如Font Awesome或Material Icons（通过国内CDN引入）
3、使用清晰的视觉层次结构，突出重要内容
4、UI界面样式用户体验极好的交互，配色方案应丰富、专业、和谐，适合长时间阅读
5、代码结构清晰，包含适当注释，便于理解和维护
6、页面必须在所有设备上（移动端、桌面端、平板设备）完美展示
7、所有网址都带有网站图标，以及用卡片形式展示
8、添加适当的微交互效果提升用户体验
9、确保页面加载速度快，避免不必要的大型资源，特别是国外资源（因为是国内访问的）
10、实现懒加载技术用于长页面内容
11、确保代码符合W3C标准，无错误警告
12、所有收录的网址都是由你来提供，内容包括天文地理，常用软件到专业软件，运动到摄影，甚至到宇宙银河（开玩笑））等等分类，只收录优质的网站，覆盖人类已知的所有的分类都需要帮我收录。
12、最少要30个类目以上，每个类目最少要收录10+个以上，甚至有些类目可以更加多，总体收录的网址最少300+个。

以下是主题样式的要求：玻璃拟态 (Glassmorphism)
- 透明度与模糊: 核心是透明背景的元素，通常带有模糊效果，看起来像磨砂玻璃。
- 多层级: 通过不同透明度和模糊度的叠加，营造出多层级的视觉效果。
- 柔和光影: 通常配合柔和的阴影和高光，增加立体感。
- 适用场景: 现代操作系统界面、UI卡片、弹出窗口等，常与扁平化或极简风格结合，增加时尚感。



###如果需要清理Cursor缓存，可以在命令提示符中运行：
注意： 清理这些目录会删除您的扩展、主题、代码片段等数据，建议先导出您的配置文件。
rd /s /q %USERPROFILE%\AppData\Local\Cursor*


C:\Users\<USER>\.cursor\extensions
C:\Users\<USER>\AppData\Local\Programs\cursor
C:\Users\<USER>\AppData\Roaming\Cursor
C:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage



按 Win + R，输入 regedit，打开注册表编辑器
搜索并删除包含"Cursor"的项目：
HKEY_CURRENT_USER\Software\Cursor
HKEY_LOCAL_MACHINE\SOFTWARE\Cursor
在注册表中搜索"Cursor"相关项目并删除


清理环境变量（如果有）
右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
检查并删除任何Cursor相关的环境变量