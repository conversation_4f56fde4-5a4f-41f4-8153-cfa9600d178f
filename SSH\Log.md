seven@DESKTOP-H8KCUUM:/mnt/c/Users/<USER>/claudia$ bun run tauri build
$ tauri build
     Running beforeBuildCommand `bun run build`
$ bun run build:executables:current
$ bun run scripts/fetch-and-build.js current --version=1.0.41
🚀 Starting Claude Code fetch and build process...
Target platform: current

🔍 Using Claude Code version from CLI argument: 1.0.41

📦 Fetching @anthropic-ai/claude-code@1.0.41 package from npm...
Downloading package tarball for version 1.0.41...
Running: npm pack @anthropic-ai/claude-code@1.0.41
npm notice
npm notice 📦  @anthropic-ai/claude-code@1.0.41
npm notice Tarball Contents
npm notice 150B LICENSE.md
npm notice 2.3kB README.md
npm notice 7.7MB cli.js
npm notice 1.2kB package.json
npm notice 966B scripts/preinstall.js
npm notice 3.3kB sdk.d.ts
npm notice 5.1kB sdk.mjs
npm notice 29.4kB vendor/claude-code-jetbrains-plugin/lib/annotations-23.0.0.jar
npm notice 806B vendor/claude-code-jetbrains-plugin/lib/claude-code-jetbrains-plugin-0.1.11-beta-searchableOptions.jar
npm notice 258.9kB vendor/claude-code-jetbrains-plugin/lib/claude-code-jetbrains-plugin-0.1.11-beta.jar
npm notice 296.0kB vendor/claude-code-jetbrains-plugin/lib/config-1.4.3.jar
npm notice 257.0kB vendor/claude-code-jetbrains-plugin/lib/jansi-2.4.1.jar
npm notice 89.2kB vendor/claude-code-jetbrains-plugin/lib/kotlin-logging-jvm-7.0.0.jar
npm notice 3.1MB vendor/claude-code-jetbrains-plugin/lib/kotlin-reflect-2.0.21.jar
npm notice 780.8kB vendor/claude-code-jetbrains-plugin/lib/kotlin-sdk-jvm-0.4.0.jar
npm notice 1.7MB vendor/claude-code-jetbrains-plugin/lib/kotlin-stdlib-2.1.20.jar
npm notice 1.5MB vendor/claude-code-jetbrains-plugin/lib/kotlinx-coroutines-core-jvm-1.9.0.jar
npm notice 4.4kB vendor/claude-code-jetbrains-plugin/lib/kotlinx-coroutines-slf4j-1.9.0.jar
npm notice 22.1kB vendor/claude-code-jetbrains-plugin/lib/kotlinx-io-bytestring-jvm-0.5.4.jar
npm notice 150.5kB vendor/claude-code-jetbrains-plugin/lib/kotlinx-io-core-jvm-0.5.4.jar
npm notice 391.8kB vendor/claude-code-jetbrains-plugin/lib/kotlinx-serialization-core-jvm-1.8.1.jar
npm notice 277.0kB vendor/claude-code-jetbrains-plugin/lib/kotlinx-serialization-json-jvm-1.8.1.jar
npm notice 110.7kB vendor/claude-code-jetbrains-plugin/lib/ktor-client-cio-jvm-3.0.2.jar
npm notice 787.7kB vendor/claude-code-jetbrains-plugin/lib/ktor-client-core-jvm-3.0.2.jar
npm notice 6.4kB vendor/claude-code-jetbrains-plugin/lib/ktor-events-jvm-3.0.2.jar
npm notice 122.7kB vendor/claude-code-jetbrains-plugin/lib/ktor-http-cio-jvm-3.0.2.jar
npm notice 401.4kB vendor/claude-code-jetbrains-plugin/lib/ktor-http-jvm-3.0.2.jar
npm notice 238.6kB vendor/claude-code-jetbrains-plugin/lib/ktor-io-jvm-3.0.2.jar
npm notice 191.3kB vendor/claude-code-jetbrains-plugin/lib/ktor-network-jvm-3.0.2.jar
npm notice 194.9kB vendor/claude-code-jetbrains-plugin/lib/ktor-network-tls-jvm-3.0.2.jar
npm notice 22.0kB vendor/claude-code-jetbrains-plugin/lib/ktor-serialization-jvm-3.0.2.jar
npm notice 101.0kB vendor/claude-code-jetbrains-plugin/lib/ktor-server-cio-jvm-3.0.2.jar
npm notice 771.3kB vendor/claude-code-jetbrains-plugin/lib/ktor-server-core-jvm-3.0.2.jar
npm notice 20.7kB vendor/claude-code-jetbrains-plugin/lib/ktor-server-sse-jvm-3.0.2.jar
npm notice 48.9kB vendor/claude-code-jetbrains-plugin/lib/ktor-server-websockets-jvm-3.0.2.jar
npm notice 4.1kB vendor/claude-code-jetbrains-plugin/lib/ktor-sse-jvm-3.0.2.jar
npm notice 330.7kB vendor/claude-code-jetbrains-plugin/lib/ktor-utils-jvm-3.0.2.jar
npm notice 7.0kB vendor/claude-code-jetbrains-plugin/lib/ktor-websocket-serialization-jvm-3.0.2.jar
npm notice 168.2kB vendor/claude-code-jetbrains-plugin/lib/ktor-websockets-jvm-3.0.2.jar
npm notice 69.4kB vendor/claude-code-jetbrains-plugin/lib/slf4j-api-2.0.16.jar
npm notice 100.2kB vendor/claude-code.vsix
npm notice 4.4MB vendor/ripgrep/arm64-darwin/rg
npm notice 8.6MB vendor/ripgrep/arm64-darwin/ripgrep.node
npm notice 5.2MB vendor/ripgrep/arm64-linux/rg
npm notice 32.2MB vendor/ripgrep/arm64-linux/ripgrep.node
npm notice 126B vendor/ripgrep/COPYING
npm notice 5.2MB vendor/ripgrep/x64-darwin/rg
npm notice 8.7MB vendor/ripgrep/x64-darwin/ripgrep.node
npm notice 6.6MB vendor/ripgrep/x64-linux/rg
npm notice 30.1MB vendor/ripgrep/x64-linux/ripgrep.node
npm notice 5.4MB vendor/ripgrep/x64-win32/rg.exe
npm notice 42.6MB vendor/ripgrep/x64-win32/ripgrep.node
npm notice 88.7kB yoga.wasm
npm notice Tarball Details
npm notice name: @anthropic-ai/claude-code
npm notice version: 1.0.41
npm notice filename: anthropic-ai-claude-code-1.0.41.tgz
npm notice package size: 56.1 MB
npm notice unpacked size: 169.3 MB
npm notice shasum: 81aa63fec8815f68b3e283f67a06ade488fc21a0
npm notice integrity: sha512-6E5SCb1I/pC4W[...]MYfsJEqTeIo2Q==
npm notice total files: 53
npm notice
anthropic-ai-claude-code-1.0.41.tgz
Found tarball: anthropic-ai-claude-code-1.0.41.tgz
Extracting package...
Running: tar -xzf anthropic-ai-claude-code-1.0.41.tgz
✓ Package extracted to: /mnt/c/Users/<USER>/claudia/temp-claude-package/package

📋 Copying required files from Claude Code package...
Copying cli.js...
Copying yoga.wasm...
Copying vendor/ directory...
Running: cp -r /mnt/c/Users/<USER>/claudia/temp-claude-package/package/vendor /mnt/c/Users/<USER>/claudia/vendor
✓ Required files copied successfully

🔨 Building executables for platform: current
Running: bun run ./scripts/build-executables.js current
Building 1 executable(s) with full optimizations...
Optimizations enabled: --minify --sourcemap

Preparing bundle with native Bun embedding...
Running: bun run scripts/prepare-bundle-native.js
Preparing CLI for native Bun embedding...
✓ Replaced yoga.wasm loading with embedded version
✓ Added embedded file handling for ripgrep
✓ Added embedded file handling for ripgrep.node

✅ Created ./cli-native-bundled.js ready for bundling with native embedding

Now you can run:
  bun build --compile --minify ./cli-native-bundled.js --outfile dist/claude-code

Building claude-code-x86_64-unknown-linux-gnu...
Running: bun build --compile --minify --sourcemap --target=bun-linux-x64 ./cli-native-bundled.js --outfile=src-tauri/binaries/claude-code-x86_64-unknown-linux-gnu
 [467ms]  minify  -0.15 GB (estimate)
 [176ms]  bundle  12 modules
[1106ms] compile  src-tauri/binaries/claude-code-x86_64-unknown-linux-gnu
✓ Built claude-code-x86_64-unknown-linux-gnu in 1.8s

✅ Successfully built 1/1 executables in 1.9s

Executables are available in the src-tauri/binaries/ directory

Notes:
- All executables include embedded assets (yoga.wasm, ripgrep binaries)
- File names follow Tauri sidecar triple naming convention (name-platform-architecture)
- Modern variants require CPUs from 2013+ (AVX2 support)
- Baseline variants support older CPUs (pre-2013)
- Musl variants are for Alpine Linux and similar distributions
- All executables are optimized with minification and sourcemaps

✓ Cleaned up temporary files

✅ Build process completed successfully in 7.0s

📁 Executables are available in the src-tauri/binaries/ directory

🧹 Cleaning up temporary files...
✓ Temporary package directory cleaned up
✓ Cleanup completed
$ tsc && vite build
vite v6.3.5 building for production...
✓ 4590 modules transformed.
[plugin vite:reporter]
(!) /mnt/c/Users/<USER>/claudia/src/lib/hooksManager.ts is dynamically imported by /mnt/c/Users/<USER>/claudia/src/lib/api.ts but also statically imported by /mnt/c/Users/<USER>/claudia/src/components/HooksEditor.tsx, dynamic import will not move module into another chunk.

dist/index.html                             1.06 kB │ gzip:   0.44 kB
dist/assets/asterisk-logo-B09BSk2Z.png    100.30 kB
dist/assets/claudia-nfo-CGCcrpzI.ogg      314.64 kB
dist/assets/editor-vendor-BJBUeiC3.css     33.82 kB │ gzip:   6.01 kB
dist/assets/index-BfbG_QdT.css             89.30 kB │ gzip:  15.10 kB
dist/assets/tauri-q-KBTfjL.js              19.35 kB │ gzip:   4.34 kB
dist/assets/utils-C7WoKIg-.js              30.27 kB │ gzip:  10.17 kB
dist/assets/ui-vendor-DX8dMwEd.js         108.81 kB │ gzip:  35.52 kB
dist/assets/react-vendor-CWc6w16D.js      141.85 kB │ gzip:  45.52 kB
dist/assets/index-VPCydQH9.js             595.88 kB │ gzip: 146.78 kB
dist/assets/syntax-vendor-ChrS0SUu.js     636.07 kB │ gzip: 230.22 kB
dist/assets/editor-vendor-Duz2AUOW.js   1,724.19 kB │ gzip: 598.86 kB
✓ built in 39.68s
   Compiling proc-macro2 v1.0.95
   Compiling unicode-ident v1.0.18
   Compiling serde v1.0.219
   Compiling libc v0.2.172
   Compiling equivalent v1.0.2
   Compiling hashbrown v0.15.4
   Compiling smallvec v1.15.1
   Compiling winnow v0.7.10
   Compiling toml_write v0.1.2
   Compiling autocfg v1.4.0
   Compiling pkg-config v0.3.32
   Compiling cfg-if v1.0.0
   Compiling heck v0.5.0
   Compiling zerocopy v0.8.25
   Compiling syn v1.0.109
   Compiling target-lexicon v0.12.16
   Compiling version-compare v0.2.0
   Compiling parking_lot_core v0.9.11
   Compiling siphasher v0.3.11
   Compiling thiserror v1.0.69
   Compiling version_check v0.9.5
   Compiling shlex v1.3.0
   Compiling once_cell v1.21.3
   Compiling pin-project-lite v0.2.16
   Compiling getrandom v0.1.16
   Compiling icu_normalizer_data v2.0.0
   Compiling icu_properties_data v2.0.1
   Compiling semver v1.0.26
   Compiling siphasher v1.0.1
   Compiling memchr v2.7.4
   Compiling futures-core v0.3.31
   Compiling stable_deref_trait v1.2.0
   Compiling indexmap v2.9.0
   Compiling phf_shared v0.8.0
   Compiling getrandom v0.3.3
   Compiling phf_shared v0.10.0
   Compiling thiserror v2.0.12
   Compiling proc-macro-hack v0.5.20+deprecated
   Compiling fnv v1.0.7
   Compiling typeid v1.0.3
   Compiling phf_shared v0.11.3
   Compiling futures-io v0.3.31
   Compiling lock_api v0.4.13
   Compiling slab v0.4.9
   Compiling litemap v0.8.0
   Compiling ident_case v1.0.1
   Compiling strsim v0.11.1
   Compiling writeable v0.6.1
   Compiling convert_case v0.4.0
   Compiling anyhow v1.0.98
   Compiling scopeguard v1.2.0
   Compiling serde_json v1.0.140
   Compiling new_debug_unreachable v1.0.6
   Compiling byteorder v1.5.0
   Compiling itoa v1.0.15
   Compiling mac v0.1.1
   Compiling ryu v1.0.20
   Compiling futures-sink v0.3.31
   Compiling precomputed-hash v0.1.1
   Compiling indexmap v1.9.3
   Compiling percent-encoding v2.3.1
   Compiling futf v0.1.5
   Compiling utf-8 v0.7.6
   Compiling dtoa v1.0.10
   Compiling log v0.4.27
   Compiling utf8_iter v1.0.4
   Compiling bitflags v1.3.2
   Compiling form_urlencoded v1.2.1
   Compiling tendril v0.4.3
   Compiling dtoa-short v0.3.5
   Compiling aho-corasick v1.1.3
   Compiling phf v0.10.1
   Compiling unic-common v0.9.0
   Compiling unic-char-range v0.9.0
   Compiling itoa v0.4.8
   Compiling camino v1.1.10
   Compiling hashbrown v0.12.3
   Compiling nodrop v0.1.14
   Compiling regex-syntax v0.8.5
   Compiling matches v0.1.10
   Compiling alloc-no-stdlib v2.0.4
   Compiling unic-char-property v0.9.0
   Compiling servo_arc v0.1.1
   Compiling alloc-stdlib v0.2.2
   Compiling unic-ucd-version v0.9.0
   Compiling futures-channel v0.3.31
   Compiling fxhash v0.2.1
   Compiling futures-task v0.3.31
   Compiling pin-utils v0.1.0
   Compiling schemars v0.8.22
   Compiling regex-automata v0.4.9
   Compiling thin-slice v0.1.1
   Compiling unic-ucd-ident v0.9.0
   Compiling brotli-decompressor v4.0.3
   Compiling dyn-clone v1.0.19
   Compiling bytes v1.10.1
   Compiling same-file v1.0.6
   Compiling dunce v1.0.5
   Compiling glob v0.3.2
   Compiling winnow v0.5.40
   Compiling proc-macro-error-attr v1.0.4
   Compiling http v1.3.1
   Compiling brotli v7.0.0
   Compiling walkdir v2.5.0
   Compiling proc-macro-error v1.0.4
   Compiling regex v1.11.1
   Compiling crossbeam-utils v0.8.21
   Compiling heck v0.4.1
   Compiling gio v0.18.4
   Compiling typenum v1.18.0
   Compiling option-ext v0.2.0
   Compiling memoffset v0.9.1
   Compiling generic-array v0.14.7
   Compiling quote v1.0.40
   Compiling cfg-expr v0.15.8
   Compiling jobserver v0.1.33
   Compiling getrandom v0.2.16
   Compiling syn v2.0.101
   Compiling cc v1.2.26
   Compiling signal-hook-registry v1.4.5
   Compiling dirs-sys v0.5.0
   Compiling socket2 v0.5.10
   Compiling mio v1.0.4
   Compiling rand_core v0.6.4
   Compiling rand_core v0.5.1
   Compiling ppv-lite86 v0.2.21
   Compiling gtk v0.18.2
   Compiling dirs v6.0.0
   Compiling rand_pcg v0.2.1
   Compiling rand_chacha v0.3.1
   Compiling rand_chacha v0.2.2
   Compiling powerfmt v0.2.0
   Compiling num-conv v0.1.0
   Compiling rand v0.8.5
   Compiling rand v0.7.3
   Compiling time-core v0.1.4
   Compiling parking_lot v0.12.4
   Compiling x11 v2.21.0
   Compiling synstructure v0.13.2
   Compiling darling_core v0.20.11
   Compiling serde_derive_internals v0.29.1
   Compiling cookie v0.18.1
   Compiling rustix v1.0.7
   Compiling simd-adler32 v0.3.7
   Compiling time-macros v0.2.22
   Compiling phf_generator v0.11.3
   Compiling phf_generator v0.8.0
   Compiling phf_generator v0.10.0
   Compiling tracing-core v0.1.34
   Compiling phf_codegen v0.10.0
   Compiling string_cache_codegen v0.5.4
   Compiling phf_codegen v0.8.0
   Compiling vcpkg v0.2.15
   Compiling linux-raw-sys v0.9.4
   Compiling adler2 v2.0.0
   Compiling cssparser v0.27.2
   Compiling html5ever v0.26.0
   Compiling miniz_oxide v0.8.8
   Compiling serde_derive v1.0.219
   Compiling zerofrom-derive v0.1.6
   Compiling yoke-derive v0.8.0
   Compiling zerovec-derive v0.11.1
   Compiling displaydoc v0.2.5
   Compiling thiserror-impl v1.0.69
   Compiling thiserror-impl v2.0.12
   Compiling cssparser-macros v0.6.1
   Compiling derive_more v0.99.20
   Compiling phf_macros v0.11.3
   Compiling selectors v0.22.0
   Compiling ctor v0.2.9
   Compiling phf_macros v0.8.0
   Compiling futures-macro v0.3.31
   Compiling schemars_derive v0.8.22
   Compiling markup5ever v0.11.0
   Compiling darling_macro v0.20.11
   Compiling tokio-macros v2.5.0
   Compiling tracing-attributes v0.1.29
   Compiling crossbeam-channel v0.5.15
   Compiling x11-dl v2.21.0
   Compiling num-traits v0.2.19
   Compiling crc32fast v1.4.2
   Compiling raw-window-handle v0.6.2
   Compiling fastrand v2.3.0
   Compiling flate2 v1.1.2
   Compiling block-buffer v0.10.4
   Compiling crypto-common v0.1.6
   Compiling fdeflate v0.3.7
   Compiling unicode-segmentation v1.12.0
   Compiling parking v2.2.1
   Compiling png v0.17.16
   Compiling digest v0.10.7
   Compiling dlopen2_derive v0.4.1
   Compiling concurrent-queue v2.5.0
   Compiling libloading v0.7.4
   Compiling tauri-runtime v2.6.0
   Compiling atomic-waker v1.1.2
   Compiling wry v0.51.2
   Compiling cpufeatures v0.2.17
   Compiling ico v0.4.0
   Compiling sha2 v0.10.9
   Compiling serde_repr v0.1.20
   Compiling bytemuck v1.23.1
   Compiling base64 v0.22.1
   Compiling tauri-runtime-wry v2.6.0
   Compiling lazy_static v1.5.0
   Compiling rustix v0.38.44
   Compiling byteorder-lite v0.1.0
   Compiling event-listener v5.4.0
   Compiling openssl-sys v0.9.109
   Compiling serialize-to-javascript-impl v0.1.1
   Compiling linux-raw-sys v0.4.15
   Compiling mime v0.3.17
   Compiling image v0.25.6
   Compiling event-listener-strategy v0.5.4
   Compiling enumflags2_derive v0.7.11
   Compiling ring v0.17.14
   Compiling http-range v0.1.5
   Compiling static_assertions v1.1.0
   Compiling http-body v1.0.1
   Compiling wayland-sys v0.31.6
   Compiling httparse v1.10.1
   Compiling zeroize v1.8.1
   Compiling rustls-pki-types v1.12.0
   Compiling futures-lite v2.6.0
   Compiling wayland-backend v0.3.10
   Compiling untrusted v0.9.0
   Compiling foreign-types-shared v0.1.1
   Compiling try-lock v0.2.5
   Compiling openssl v0.10.73
   Compiling tower-service v0.3.3
   Compiling want v0.3.1
   Compiling foreign-types v0.3.2
   Compiling openssl-macros v0.1.1
   Compiling quick-xml v0.37.5
   Compiling wayland-client v0.31.10
   Compiling rustls v0.23.28
   Compiling native-tls v0.2.14
   Compiling downcast-rs v1.2.1
   Compiling wayland-scanner v0.31.6
   Compiling async-lock v3.4.0
   Compiling async-task v4.7.1
   Compiling ipnet v2.11.0
   Compiling subtle v2.6.1
   Compiling cfg_aliases v0.2.1
   Compiling endi v1.1.0
   Compiling openssl-probe v0.1.6
   Compiling nix v0.30.1
   Compiling async-channel v2.3.1
   Compiling zstd-sys v2.0.15+zstd.1.5.7
   Compiling futures-util v0.3.31
   Compiling ahash v0.8.12
   Compiling futures-executor v0.3.31
   Compiling phf v0.8.0
   Compiling sync_wrapper v1.0.2
   Compiling tower-layer v0.3.3
   Compiling phf v0.11.3
   Compiling litrs v0.4.1
   Compiling psl-types v2.0.11
   Compiling minimal-lexical v0.2.1
   Compiling x11rb-protocol v0.13.1
   Compiling fixedbitset v0.4.2
   Compiling tokio v1.45.1
   Compiling nom v7.1.3
   Compiling document-features v0.2.11
   Compiling petgraph v0.6.5
   Compiling webpki-roots v1.0.1
   Compiling http-body-util v0.1.3
   Compiling piper v0.2.4
   Compiling dlopen2 v0.7.0
   Compiling os_pipe v1.2.2
   Compiling encoding_rs v0.8.35
   Compiling iri-string v0.7.8
   Compiling tree_magic_mini v3.1.6
   Compiling blocking v1.6.1
   Compiling async-executor v1.13.2
   Compiling async-broadcast v0.7.2
   Compiling libsqlite3-sys v0.30.1
   Compiling async-trait v0.1.88
   Compiling ordered-stream v0.2.0
   Compiling tokio-util v0.7.15
   Compiling tower v0.5.2
   Compiling is-docker v0.2.0
   Compiling zstd-safe v7.2.4
   Compiling utf8parse v0.2.2
   Compiling hex v0.4.3
   Compiling rfd v0.15.3
   Compiling anstyle-parse v0.2.7
   Compiling is-wsl v0.4.0
   Compiling hashbrown v0.14.5
   Compiling filetime v0.2.25
   Compiling is_terminal_polyfill v1.70.1
   Compiling anstyle v1.0.11
   Compiling pathdiff v0.2.3
   Compiling xkeysym v0.2.1
   Compiling anstyle-query v1.1.3
   Compiling colorchoice v1.0.4
   Compiling open v5.3.2
   Compiling hashlink v0.9.1
   Compiling dirs-sys v0.4.1
   Compiling anstream v0.6.19
   Compiling env_filter v0.1.3
   Compiling shared_child v1.0.2
   Compiling fallible-iterator v0.3.0
   Compiling unsafe-libyaml v0.2.11
   Compiling env_home v0.1.0
   Compiling iana-time-zone v0.1.63
   Compiling minisign-verify v0.2.4
   Compiling fallible-streaming-iterator v0.1.9
   Compiling data-url v0.3.1
   Compiling jiff v0.2.14
   Compiling either v1.15.0
   Compiling dirs v5.0.1
   Compiling futures v0.3.31
   Compiling env_logger v0.11.8
   Compiling tracing v0.1.41
   Compiling h2 v0.4.10
   Compiling zerofrom v0.1.6
   Compiling yoke v0.8.0
   Compiling zerovec v0.11.2
   Compiling zerotrie v0.2.2
   Compiling tinystr v0.8.1
   Compiling potential_utf v0.1.2
   Compiling icu_collections v2.0.0
   Compiling icu_locale_core v2.0.0
   Compiling hyper v1.6.0
   Compiling icu_provider v2.0.0
   Compiling darling v0.20.11
   Compiling icu_properties v2.0.1
   Compiling icu_normalizer v2.0.0
   Compiling serde_with_macros v3.12.0
   Compiling toml_datetime v0.6.11
   Compiling serde_spanned v0.6.9
   Compiling uuid v1.17.0
   Compiling string_cache v0.8.9
   Compiling cargo-platform v0.1.9
   Compiling erased-serde v0.4.6
   Compiling zvariant_utils v3.2.0
   Compiling enumflags2 v0.7.11
   Compiling bitflags v2.9.1
   Compiling deranged v0.4.0
   Compiling dpi v0.1.2
   Compiling toml_edit v0.22.27
   Compiling cfb v0.7.3
   Compiling rustc_version v0.4.1
   Compiling serde-untagged v0.1.7
   Compiling toml_edit v0.20.7
   Compiling toml_edit v0.19.15
   Compiling idna_adapter v1.2.1
   Compiling jsonptr v0.6.3
   Compiling cargo_metadata v0.19.2
   Compiling keyboard-types v0.7.0
   Compiling time v0.3.41
   Compiling infer v0.19.0
   Compiling json-patch v3.0.1
   Compiling field-offset v0.3.6
   Compiling serialize-to-javascript v0.1.1
   Compiling idna v1.0.3
   Compiling proc-macro-crate v2.0.0
   Compiling proc-macro-crate v1.3.1
   Compiling toml v0.8.23
   Compiling proc-macro-crate v3.3.0
   Compiling hyper-util v0.1.14
   Compiling system-deps v6.2.2
   Compiling embed-resource v3.0.3
   Compiling cargo_toml v0.22.1
   Compiling url v2.5.4
   Compiling glib-macros v0.18.5
   Compiling gtk3-macros v0.18.2
   Compiling polling v3.8.0
   Compiling rustls-webpki v0.103.3
   Compiling tempfile v3.20.0
   Compiling zvariant_derive v5.5.3
   Compiling tauri-winres v0.3.1
   Compiling publicsuffix v2.3.0
   Compiling async-io v2.4.1
   Compiling x11rb v0.13.1
   Compiling tower-http v0.6.6
   Compiling glib-sys v0.18.1
   Compiling gobject-sys v0.18.0
   Compiling gio-sys v0.18.1
   Compiling urlpattern v0.3.0
   Compiling gdk-sys v0.18.2
   Compiling atk-sys v0.18.2
   Compiling cairo-sys-rs v0.18.2
   Compiling gdk-pixbuf-sys v0.18.0
   Compiling pango-sys v0.18.0
   Compiling gtk-sys v0.18.2
   Compiling soup3-sys v0.5.0
   Compiling javascriptcore-rs-sys v1.1.1
   Compiling webkit2gtk-sys v2.0.1
   Compiling gdkx11-sys v0.18.2
   Compiling tokio-rustls v0.26.2
   Compiling async-signal v0.2.11
   Compiling cookie_store v0.21.1
   Compiling hyper-rustls v0.27.7
   Compiling wayland-protocols v0.32.8
   Compiling serde_urlencoded v0.7.1
   Compiling async-process v2.3.1
   Compiling xattr v1.5.0
   Compiling tar v0.4.44
   Compiling global-hotkey v0.7.0
   Compiling which v7.0.3
   Compiling serde_yaml v0.9.34+deprecated
   Compiling wayland-protocols-wlr v0.3.8
   Compiling chrono v0.4.41
   Compiling wl-clipboard-rs v0.9.2
   Compiling arboard v3.5.0
   Compiling zstd v0.13.3
   Compiling tokio-native-tls v0.3.1
   Compiling hyper-tls v0.6.0
   Compiling reqwest v0.12.20
   Compiling kuchikiki v0.8.2
   Compiling serde_with v3.12.0
   Compiling rusqlite v0.32.1
   Compiling tauri-utils v2.4.0
   Compiling gdkwayland-sys v0.18.2
   Compiling tauri-plugin v2.2.0
   Compiling tauri-build v2.2.0
   Compiling tauri-codegen v2.2.0
   Compiling zvariant v5.5.3
   Compiling tauri v2.5.1
   Compiling tauri-macros v2.2.0
   Compiling tauri-plugin-fs v2.3.0
   Compiling tauri-plugin-updater v2.8.1
   Compiling tauri-plugin-global-shortcut v2.2.1
   Compiling tauri-plugin-dialog v2.2.2
   Compiling tauri-plugin-process v2.2.2
   Compiling tauri-plugin-shell v2.2.1
   Compiling tauri-plugin-http v2.4.4
   Compiling tauri-plugin-notification v2.2.3
   Compiling tauri-plugin-clipboard-manager v2.2.3
   Compiling claudia v0.1.0 (/mnt/c/Users/<USER>/claudia/src-tauri)
   Compiling zbus_names v4.2.0
   Compiling zbus_macros v5.7.1
   Compiling libappindicator-sys v0.9.0
   Compiling glib v0.18.5
   Compiling cairo-rs v0.18.5
   Compiling atk v0.18.2
   Compiling javascriptcore-rs v1.1.2
   Compiling zbus v5.7.1
   Compiling notify-rust v4.11.7
   Compiling pango v0.18.3
   Compiling gdk-pixbuf v0.18.5
   Compiling soup3 v0.5.0
   Compiling gdk v0.18.2
   Compiling gdkx11 v0.18.2
   Compiling webkit2gtk v2.0.1
   Compiling muda v0.16.1
   Compiling tao v0.33.0
   Compiling libappindicator v0.9.0
   Compiling tray-icon v0.20.1
    Finished `release` profile [optimized] target(s) in 4m 58s
       Built application at: /mnt/c/Users/<USER>/claudia/src-tauri/target/release/claudia
    Bundling Claudia_0.1.0_amd64.deb (/mnt/c/Users/<USER>/claudia/src-tauri/target/release/bundle/deb/Claudia_0.1.0_amd64.deb)
    Bundling Claudia-0.1.0-1.x86_64.rpm (/mnt/c/Users/<USER>/claudia/src-tauri/target/release/bundle/rpm/Claudia-0.1.0-1.x86_64.rpm)