# 仙途无界 - 项目目录结构

## 完整项目结构

```
RPG/
├── docs/                           # 项目文档
│   ├── 项目需求文档.md              # 完整需求文档
│   ├── 项目目录结构.md              # 本文档
│   ├── API文档.md                  # API接口文档
│   ├── 数据库设计.md                # 数据库结构设计
│   └── 开发日志.md                 # 开发进度记录
│
├── src/                           # 前端源代码
│   ├── assets/                    # 静态资源
│   │   ├── images/               # 图片资源
│   │   │   ├── backgrounds/      # 背景图片
│   │   │   ├── characters/       # 角色立绘
│   │   │   ├── items/           # 物品图标
│   │   │   └── ui/              # UI图标
│   │   ├── fonts/               # 字体文件
│   │   ├── sounds/              # 音效文件 (可选)
│   │   └── data/                # 静态数据文件
│   │       ├── items.json       # 物品数据
│   │       ├── skills.json      # 技能数据
│   │       ├── quests.json      # 任务数据
│   │       └── story.json       # 剧情文本
│   │
│   ├── components/               # Vue组件
│   │   ├── common/              # 通用组件
│   │   │   ├── AppHeader.vue    # 应用头部
│   │   │   ├── AppSidebar.vue   # 侧边栏
│   │   │   ├── LoadingSpinner.vue # 加载动画
│   │   │   └── Modal.vue        # 弹窗组件
│   │   ├── game/                # 游戏专用组件
│   │   │   ├── Character/       # 角色相关组件
│   │   │   │   ├── CharacterPanel.vue    # 角色面板
│   │   │   │   ├── AttributeDisplay.vue  # 属性显示
│   │   │   │   ├── EquipmentSlots.vue    # 装备槽位
│   │   │   │   └── SkillTree.vue         # 技能树
│   │   │   ├── Story/           # 剧情相关组件
│   │   │   │   ├── StoryText.vue        # 剧情文本
│   │   │   │   ├── ChoiceButtons.vue    # 选择按钮
│   │   │   │   └── DialogueBox.vue      # 对话框
│   │   │   ├── Battle/          # 战斗相关组件
│   │   │   │   ├── BattleScene.vue      # 战斗场景
│   │   │   │   ├── SkillBar.vue         # 技能栏
│   │   │   │   ├── StatusEffects.vue    # 状态效果
│   │   │   │   └── BattleLog.vue        # 战斗日志
│   │   │   ├── Inventory/       # 背包相关组件
│   │   │   │   ├── ItemGrid.vue         # 物品网格
│   │   │   │   ├── ItemDetail.vue       # 物品详情
│   │   │   │   └── ItemFilter.vue       # 物品筛选
│   │   │   ├── Cultivation/     # 修炼相关组件
│   │   │   │   ├── CultivationPanel.vue # 修炼面板
│   │   │   │   ├── SpiritualRoots.vue   # 灵根显示
│   │   │   │   └── RealmProgress.vue     # 境界进度
│   │   │   └── Shop/            # 商店相关组件
│   │   │       ├── ShopList.vue         # 商店列表
│   │   │       ├── ShopItem.vue         # 商店物品
│   │   │       └── PurchaseModal.vue    # 购买弹窗
│   │   └── ui/                  # UI基础组件
│   │       ├── Button.vue       # 按钮组件
│   │       ├── Input.vue        # 输入框组件
│   │       ├── Card.vue         # 卡片组件
│   │       ├── Progress.vue     # 进度条组件
│   │       └── Tooltip.vue      # 提示框组件
│   │
│   ├── views/                   # 页面组件
│   │   ├── auth/               # 认证页面
│   │   │   ├── Login.vue       # 登录页面
│   │   │   ├── Register.vue    # 注册页面
│   │   │   └── Profile.vue     # 个人资料
│   │   ├── game/               # 游戏主要页面
│   │   │   ├── Home.vue        # 游戏主页
│   │   │   ├── CharacterCreate.vue  # 角色创建
│   │   │   ├── GameMain.vue    # 游戏主界面
│   │   │   ├── Story.vue       # 剧情页面
│   │   │   ├── Battle.vue      # 战斗页面
│   │   │   ├── Inventory.vue   # 背包页面
│   │   │   ├── Cultivation.vue # 修炼页面
│   │   │   ├── Shop.vue        # 商店页面
│   │   │   └── Achievement.vue # 成就页面
│   │   └── system/             # 系统页面
│   │       ├── Settings.vue    # 设置页面
│   │       ├── Save.vue        # 存档管理
│   │       └── About.vue       # 关于页面
│   │
│   ├── stores/                 # Pinia状态管理
│   │   ├── auth.ts            # 用户认证状态
│   │   ├── character.ts       # 角色数据状态
│   │   ├── game.ts            # 游戏状态
│   │   ├── story.ts           # 剧情进度状态
│   │   ├── battle.ts          # 战斗状态
│   │   ├── inventory.ts       # 背包状态
│   │   └── settings.ts        # 设置状态
│   │
│   ├── services/              # 业务逻辑服务
│   │   ├── api/               # API接口服务
│   │   │   ├── auth.ts        # 认证API
│   │   │   ├── character.ts   # 角色API
│   │   │   ├── story.ts       # 剧情API
│   │   │   ├── battle.ts      # 战斗API
│   │   │   └── supabase.ts    # Supabase客户端
│   │   ├── game/              # 游戏逻辑服务
│   │   │   ├── characterLogic.ts     # 角色逻辑
│   │   │   ├── battleLogic.ts        # 战斗逻辑
│   │   │   ├── cultivationLogic.ts   # 修炼逻辑
│   │   │   ├── itemLogic.ts          # 物品逻辑
│   │   │   └── storyLogic.ts         # 剧情逻辑
│   │   └── utils/             # 工具函数
│   │       ├── constants.ts   # 常量定义
│   │       ├── helpers.ts     # 辅助函数
│   │       ├── validators.ts  # 验证函数
│   │       └── formatters.ts  # 格式化函数
│   │
│   ├── types/                 # TypeScript类型定义
│   │   ├── auth.ts           # 认证相关类型
│   │   ├── character.ts      # 角色相关类型
│   │   ├── game.ts           # 游戏相关类型
│   │   ├── story.ts          # 剧情相关类型
│   │   ├── battle.ts         # 战斗相关类型
│   │   ├── item.ts           # 物品相关类型
│   │   └── common.ts         # 通用类型
│   │
│   ├── router/               # 路由配置
│   │   ├── index.ts          # 主路由配置
│   │   ├── guards.ts         # 路由守卫
│   │   └── routes.ts         # 路由定义
│   │
│   ├── styles/               # 样式文件
│   │   ├── main.scss         # 主样式文件
│   │   ├── variables.scss    # SCSS变量
│   │   ├── mixins.scss       # SCSS混入
│   │   ├── components.scss   # 组件样式
│   │   └── tailwind.css      # Tailwind CSS V4.1（通过国内CDN引入）
│   │
│   ├── plugins/              # Vue插件
│   │   ├── element-plus.ts   # Element Plus配置
│   │   └── directives.ts     # 自定义指令
│   │
│   ├── App.vue               # 根组件
│   ├── main.ts               # 应用入口
│   └── env.d.ts              # 环境变量类型
│
├── public/                   # 公共静态文件
│   ├── favicon.ico          # 网站图标
│   ├── index.html           # HTML模板
│   └── manifest.json        # PWA配置文件
│
├── database/                # 数据库相关
│   ├── migrations/          # 数据库迁移文件
│   │   ├── 001_initial_schema.sql     # 初始表结构
│   │   ├── 002_add_character_data.sql # 角色数据表
│   │   └── 003_add_story_data.sql     # 剧情数据表
│   ├── seeds/               # 初始数据
│   │   ├── items.sql        # 物品数据
│   │   ├── skills.sql       # 技能数据
│   │   └── quests.sql       # 任务数据
│   └── functions/           # Supabase云函数
│       ├── character_level_up.sql     # 角色升级函数
│       └── battle_calculation.sql     # 战斗计算函数
│
├── tests/                   # 测试文件
│   ├── unit/                # 单元测试
│   │   ├── components/      # 组件测试
│   │   ├── services/        # 服务测试
│   │   └── utils/           # 工具函数测试
│   ├── e2e/                 # 端到端测试
│   │   ├── auth.spec.ts     # 认证测试
│   │   ├── character.spec.ts # 角色测试
│   │   └── story.spec.ts    # 剧情测试
│   └── fixtures/            # 测试数据
│       ├── characters.json  # 测试角色数据
│       └── items.json       # 测试物品数据
│
├── scripts/                 # 构建和部署脚本
│   ├── build.sh            # 构建脚本
│   ├── deploy.sh           # 部署脚本
│   └── data-import.js      # 数据导入脚本
│
├── .github/                # GitHub配置
│   └── workflows/          # GitHub Actions
│       ├── ci.yml          # 持续集成
│       └── deploy.yml      # 自动部署
│
├── .vscode/                # VSCode配置
│   ├── settings.json       # 编辑器设置
│   ├── extensions.json     # 推荐扩展
│   └── launch.json         # 调试配置
│
├── .env.example            # 环境变量示例
├── .env.local              # 本地环境变量
├── .gitignore              # Git忽略文件
├── package.json            # 项目依赖
├── package-lock.json       # 依赖锁定文件
├── tsconfig.json           # TypeScript配置
├── vite.config.ts          # Vite构建配置
├── tailwind.config.js      # Tailwind CSS配置
├── eslint.config.js        # ESLint配置
├── prettier.config.js      # Prettier配置
├── vitest.config.ts        # 测试配置
└── README.md               # 项目说明文档
```

## 关键文件说明

### 配置文件
- **package.json**: 项目依赖和脚本配置
- **vite.config.ts**: Vite构建工具配置
- **tsconfig.json**: TypeScript编译配置
- **tailwind.config.js**: Tailwind CSS样式配置

### 环境配置
- **.env.example**: 环境变量模板
- **.env.local**: 本地开发环境变量
- **supabase配置**: 在src/services/api/supabase.ts中配置

### 重要目录
- **src/components/game/**: 游戏核心组件，按功能模块划分
- **src/stores/**: 状态管理，使用Pinia管理应用状态
- **src/services/**: 业务逻辑和API接口封装
- **src/types/**: TypeScript类型定义，确保类型安全
- **database/**: 数据库相关文件，包括表结构和初始数据

### 开发工具
- **tests/**: 完整的测试体系，包括单元测试和E2E测试
- **scripts/**: 自动化脚本，简化开发和部署流程
- **.github/workflows/**: CI/CD配置，自动化构建和部署

## 开发流程建议

1. **初始化项目**: 根据此结构创建项目骨架
2. **配置环境**: 设置Supabase和环境变量
3. **开发顺序**: 按照需求文档的开发计划逐步实现
4. **测试驱动**: 为每个功能模块编写对应测试
5. **持续集成**: 使用GitHub Actions自动化测试和部署 