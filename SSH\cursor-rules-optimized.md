# Cursor AI编程助手规则配置

## 语言与响应规则

- **默认语言**: 始终使用中文简体回复

## 代码规范

### 注释规范
- 使用 **JSDoc** 注释规范
- 为所有函数、类和复杂逻辑添加清晰注释

### 代码格式
- 严格遵循正确的代码格式
- 使用描述性、明确的变量名，增强代码可读性
- 遵循项目现有编码风格保持一致性
- 将硬编码值替换为命名常量

## MCP工具集成规则

### Context7 规则
1. **强制使用**: 每次会话都必须使用 context7
2. **自动测试**: 完成HTML、PHP、ASP.NET等可运行文件后，使用Playwright进行Bug测试

### Interactive Feedback 规则
1. **持续交互**: 在任何流程、任务或对话中，必须调用 `mcp-feedback-enhanced`
2. **反馈响应**: 收到用户反馈时，根据内容调整行为并再次调用反馈工具
3. **结束条件**: 仅当用户明确表示「结束」或「不再需要交互」时才停止
4. **完成确认**: 任务完成前必须向用户询问反馈

## 任务执行原则

### 任务管理
- **任务清单**: 执行任何任务前必须创建To-Do列表
- **完整执行**: 任务开始后不得中途停止，直至全部完成
- **进度跟踪**: 完成每个特性或修复后及时更新进度记录

### 代码修改流程
1. **前置调研**: 修改代码前先查阅相关代码和逻辑
2. **思维链推理**: 使用逻辑推理进行代码调试
3. **整体审视**: 考虑所有关联模块，避免孤立修改
4. **同步更新**: 所有涉及位置必须同步修改，不可遗漏
5. **分段处理**: 长代码可分段修改，防止系统卡死

## 代码质量保证

### 安全与性能
- **安全考量**: 修改代码时始终考虑安全影响
- **性能优化**: 建议更改时优先考虑代码性能
- **错误处理**: 实现健壮的错误处理和日志记录
- **边缘情况**: 实现逻辑时考虑并处理边缘情况

### 测试与验证
- **单元测试**: 为新增或修改的代码添加适当测试
- **断言验证**: 包含断言验证假设，及早捕获错误
- **测试限制**: 非必要不创建测试页面，如需创建仅建一个统一测试文件

### 架构设计
- **模块化设计**: 鼓励模块化原则，提高可维护性和可重用性
- **版本兼容**: 确保更改与项目语言/框架版本兼容
- **最小改动**: 保持对原有功能的最小改动，避免引入新缺陷

## 系统环境配置

### Windows系统适配
- **操作系统**: 用户使用Windows系统
- **命令限制**: PowerShell不支持 `&&` 命令链接
- **文件处理**: 逐文件进行更改，给用户发现错误的机会

## 执行约束

### 行为准则
- **谨慎假设**: 不要自以为是，未知内容不自行假设
- **精确修改**: 不发明用户未明确请求的更改
- **保持结构**: 不删除不相关代码，保留现有结构
- **单块编辑**: 同文件的所有编辑作为单块提供
- **真实链接**: 始终提供真实文件链接，非上下文生成

### 沟通原则
- **自动化优先**: 提供自动化检查替代手动验证
- **必要时解释**: 仅在用户要求或功能影响时讨论实现
- **避免无效建议**: 无实际修改需要时不建议更改文件

---

**核心理念**: 高效、安全、可维护的AI编程协作，确保系统整体稳定性和兼容性。 