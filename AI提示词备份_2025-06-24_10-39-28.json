{"prompts": [{"id": "prompt_1750369417218", "title": "Rocky Linux系统 Python Node快速安装指南", "content": "# Rocky Linux 快速安装指南\n\n## 🚀 一键配置国内镜像源\n\n```bash\n# 配置阿里云镜像源（一条命令搞定）\nsudo sed -e 's|^mirrorlist=|#mirrorlist=|g' -e 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' -i.bak /etc/yum.repos.d/rocky-*.repo && sudo dnf clean all && sudo dnf makecache\n```\n\n## 🐍 安装 Python 3.11\n\n```bash\n# 安装必要依赖和Python 3.11\nsudo dnf install python3.11 python3.11-pip python3.11-devel -y\n\n# 配置pip国内镜像\nmkdir -p ~/.pip && echo -e \"[global]\\nindex-url = https://mirrors.aliyun.com/pypi/simple/\\ntrusted-host = mirrors.aliyun.com\" > ~/.pip/pip.conf\n\n# 验证安装\npython3.11 --version && pip3.11 --version\n```\n\n## 🟢 安装 Node.js 20 LTS\n\n```bash\n# 添加NodeSource仓库并安装\ncurl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash - && sudo dnf install nodejs -y\n\n# 配置npm国内镜像\nnpm config set registry https://registry.npmmirror.com\n\n# 验证安装\nnode --version && npm --version\n```\n\n## ✅ 验证所有安装\n\n```bash\necho \"=== 系统信息 ===\" && cat /etc/rocky-release\necho \"=== Python版本 ===\" && python3.11 --version\necho \"=== Node.js版本 ===\" && node --version\necho \"=== npm版本 ===\" && npm --version\necho \"=== 安装完成 ===\"\n```\n\n## 📋 完整安装脚本（复制粘贴即可）\n\n```bash\n#!/bin/bash\necho \"开始安装Python 3.11 和 Node.js 20 LTS...\"\n\n# 1. 配置镜像源\nsudo sed -e 's|^mirrorlist=|#mirrorlist=|g' -e 's|^#baseurl=http://dl.rockylinux.org/$contentdir|baseurl=https://mirrors.aliyun.com/rockylinux|g' -i.bak /etc/yum.repos.d/rocky-*.repo\nsudo dnf clean all && sudo dnf makecache\n\n# 2. 安装Python 3.11\nsudo dnf install python3.11 python3.11-pip python3.11-devel -y\nmkdir -p ~/.pip && echo -e \"[global]\\nindex-url = https://mirrors.aliyun.com/pypi/simple/\\ntrusted-host = mirrors.aliyun.com\" > ~/.pip/pip.conf\n\n# 3. 安装Node.js 20 LTS\ncurl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -\nsudo dnf install nodejs -y\nnpm config set registry https://registry.npmmirror.com\n\n# 4. 验证安装\necho \"=== 安装完成，版本信息 ===\"\npython3.11 --version\nnode --version\nnpm --version\necho \"=== 全部完成！ ===\"\n```\n\n## 💡 使用提示\n\n- 复制上面的完整脚本，粘贴到终端运行即可\n- 整个安装过程大约需要5-10分钟\n- 如果遇到权限问题，确保使用了 `sudo`\n- 安装后可以直接使用 `python3.11` 和 `node` 命令\n\n## 🔧 常用命令\n\n```bash\n# Python虚拟环境\npython3.11 -m venv myproject\nsource myproject/bin/activate\n\n# npm包管理\nnpm install package-name\nnpm install -g package-name  # 全局安装\n```", "categoryId": "cat_1750365903279"}, {"id": "prompt_1750366164373", "title": "utubun系统 Python终端安装方法", "content": "### 检查Python3版本\npython3 --version\n\n# 检查pip版本\npip3 --version\n\n# 查看Python安装路径\nwhich python3\n\n### 添加Deadsnakes PPA 添加PPA源\nsudo add-apt-repository ppa:deadsnakes/ppa\n\n# 更新软件包列表\nsudo apt update\n\n### 安装Python 3.13完整版本（包含所有模块）\nsudo apt install python3.13-full\n\n# 或者安装基础版本\nsudo apt install python3.13\n\n# 安装开发包（推荐，包含头文件）\nsudo apt install python3.13-dev\n\n# 验证安装\npython3.13 --version\n\n###配置pip\n# 为Python 3.13安装pip\npython3.13 -m ensurepip --upgrade\n\n# 升级pip到最新版\npython3.13 -m pip install --upgrade pip\n\n# 验证pip\npython3.13 -m pip --version\n\n###把Python更新到你想要的版本\n# 将Python 3.10和3.13都添加到alternatives\nsudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.10 1\nsudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.13 2\n\n# 选择默认版本（会显示菜单让您选择）\nsudo update-alternatives --config python3\n\n# 验证\npython3 --version", "categoryId": "cat_1750365903279"}, {"id": "prompt_1750366030635", "title": "utubun系统 Node终端安装方法", "content": "### 卸载当前版本\nsudo apt remove nodejs npm\n\n# 清理残留文件\nsudo apt autoremove\n\n### 查找并删除旧的Node.js文件\nsudo find / -name \"node\" -type f 2>/dev/null\nsudo find / -name \"npm\" -type f 2>/dev/null\n\n# 删除可能的安装位置\nsudo rm -rf /usr/local/bin/node\nsudo rm -rf /usr/local/bin/npm\nsudo rm -rf /usr/local/lib/node_modules\nsudo rm -rf ~/.npm\nsudo rm -rf ~/.node-gyp\n\n### 下载并运行NodeSource设置脚本（Node.js 22）\ncurl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -\n\n# 安装Node.js 22\nsudo apt-get install -y nodejs\n\n# 验证版本\nnode -v\nnpm -v", "categoryId": "cat_1750365903279"}, {"id": "prompt_1750365917012", "title": "Docker终端安装方法", "content": "# 第1步：系统更新\nsudo dnf update -y\nsudo dnf install -y dnf-plugins-core\n\n# 第2步：添加阿里云Docker仓库\nsudo dnf config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo\n\n# 第3步：替换仓库地址为阿里云镜像\nsudo sed -i 's|download.docker.com|mirrors.aliyun.com/docker-ce|g' /etc/yum.repos.d/docker-ce.repo\n\n# 第4步：更新缓存并安装\nsudo dnf makecache\nsudo dnf install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin\n\n# 第5步：启动服务\nsudo systemctl enable docker --now\n\n### 使用一键安装脚本\n# 使用国内加速脚本\nbash <(curl -sSL https://linuxmirrors.cn/docker.sh)\n\n# 或者使用官方脚本\ncurl -fsSL https://get.docker.com | bash -s docker", "categoryId": "cat_1750365903279"}, {"id": "prompt_1750189611094", "title": "玻璃拟态 (Glassmorphism)", "content": "特点:\n透明度与模糊: 核心是透明背景的元素，通常带有模糊效果，看起来像磨砂玻璃。\n多层级: 通过不同透明度和模糊度的叠加，营造出多层级的视觉效果。\n柔和光影: 通常配合柔和的阴影和高光，增加立体感。\n适用场景: 现代操作系统界面、UI卡片、弹出窗口等，常与扁平化或极简风格结合，增加时尚感。", "categoryId": "cat_1749835881685"}, {"id": "prompt_1750189580101", "title": "扁平化设计 (Flat Design)", "content": "特点:\n简洁明了: 移除了阴影、渐变、纹理等三维效果，强调二维平面元素。\n色彩鲜明: 常用大胆、明亮的色彩。\n极简主义: 专注于功能性，减少不必要的装饰。\n排版清晰: 强调大字体和清晰的文本。\n易于响应: 扁平化设计在不同屏幕尺寸上更容易实现响应式布局。\n适用场景: 各种类型的网站、移动应用，尤其适合追求简洁、现代感和快速加载速度的产品。", "categoryId": "cat_1749835881685"}, {"id": "prompt_1750009371546", "title": "CMS", "content": "开发一个类似WordPress但技术更先进，高性能、轻量化的CMS后端系统。\n考虑到CMS系统的复杂性，建议采用分层设计。核心系统要轻量，通过插件机制扩展功能。\n数据库方面需权衡灵活性和性能：PostgreSQL 的 JSONB 类型在成熟的关系型能力与 NoSQL 式灵活性之间取得了最佳平衡，是推荐方案。API优先设计是基础，确保未来无缝支持多终端前端。\n技术栈偏向使用：Node.js (NestJS) + TypeScript + PostgreSQL (JSONB) + GraphQL + Redis + React (管理界面)，该组合在现代化、性能、开发效率与生态间达到理想平衡，是打造面向未来的 Headless CMS 的强力选择。\n\n核心设计原则：\nHeadless 架构 (API-First)：核心引擎轻量稳定，仅通过 REST/GraphQL API 提供数据与服务。\n前后端完全分离：前端（如 React/Vue 管理后台、Next.js/Nuxt.js 网站、移动 App）独立开发部署，与后端解耦。\n插件化扩展 (Microkernel)：所有非核心功能（内容类型、SEO、表单、电商等）均通过插件实现，需设计标准化的插件接口与事件钩子 (Hooks)。\n渐进式构建：优先实现可扩展的核心引擎（用户/权限/内容模型/API/插件系统），非核心功能后续通过插件添加。\n事件驱动架构：核心服务通过事件总线发布关键事件（如内容发布、用户注册），插件或内部服务监听并响应，实现解耦。\n高性能基石：异步队列处理耗时任务（如图片转码），Redis 缓存热点数据，Elasticsearch/OpenSearch 提供专业全文搜索。\n\n核心模块划分\n用户与认证模块：RBAC/ABAC 权限控制、OAuth/JWT 集成\n内容核心引擎：灵活内容建模、JSONB 存储、版本控制、工作流\n媒体管理模块：对接对象存储（S3/MinIO）、异步处理队列\n搜索模块（基于 Elasticsearch/OpenSearch）：全文检索、高亮、聚合\n插件/扩展模块：生命周期管理、Hook/Event 系统、沙箱安全隔离（可选）\n设置与配置模块：全局配置、多站点支持\n任务队列模块：保障核心 API 响应速度，解耦耗时操作", "categoryId": "cat_2"}, {"id": "prompt_1749836069076", "title": "吉卜力", "content": "## 内容要求\n- 保持核心信息，但以更易读、可视化的方式呈现\n- 版权信息和年份\n## 设计风格\n- 构建具有吉卜力风格的用户界面，既保留现代UI的功能性和可用性，又融入吉卜力的童话般美学\n- 使用清晰的视觉层次结构，突出重要内容\n- 配色方案应专业、和谐，适合长时间阅读\n- 柔和自然的色调：以淡蓝、柔和的绿色、温暖的棕色和淡粉色为主\n- 高对比度但不刺眼：明暗对比鲜明但过渡自然\n- 色彩层次丰富：通过细微的色调变化创造深度\n- 反扁平化设计（3D景深与2D手绘融合）\n- 叙事性界面（页面转场的故事线索）\n## 视觉元素\n- 自然与魔法的融合：植物、云朵、飞行物等元素的有机结合\n- 手绘质感：线条不完美但充满生命力\n- 纹理细腻：微妙的纹理增加表面细节和温暖感\n- 自然质感（水波纹/植被/云层算法）\n- 色彩法则（莫兰迪色系+光晕叠加）\n- 动态呼吸感（0.5秒延迟动画）\n## 技术规范\n- 使用HTML5、TailwindCSS 3.0+（通过国内CDN引入）和必要的JavaScript\n- 代码结构清晰，包含适当注释，便于理解和维护\n- SVG滤镜实现柔光效果（模拟赛璐璐质感）\n- CSS混合模式创建水彩叠加\n- WebGL渲染优化（保持60FPS的精灵动画）\n## 响应式设计\n- 页面必须在所有设备上（手机、平板、桌面）完美展示\n- 针对不同屏幕尺寸优化布局和字体大小\n- 确保移动端有良好的触控体验\n- 流畅而魔幻的动效：如同吉卜力动画中的场景转换\n- 智能生成组件（带铅笔纹理的按钮）\n- 动态背景渲染（实时云层运动算法）\n- 自适应布局（魔法森林般的响应式规则）\n## 图标与视觉元素\n- 使用专业图标库如Font Awesome或Material Icons（通过国内CDN引入）\n- 根据内容主题选择合适的插图或图表展示数据\n- 避免使用emoji作为主要图标\n## 交互体验\n- 添加适当的微交互效果提升用户体验：    \n * 按钮悬停时有轻微放大和颜色变化    \n * 卡片元素悬停时有精致的阴影和边框效果    \n * 页面滚动时有平滑过渡效果    \n * 内容区块加载时有优雅的淡入动画\n## 性能优化\n- 确保页面加载速度快，避免不必要的大型资源\n- 实现懒加载技术用于长页面内容\n## 输出要求\n- 提供完整可运行的单一HTML文件，包含所有必要的CSS和JavaScript\n- 确保代码符合W3C标准，无错误警告\n- 页面在不同浏览器中保持一致的外观和功能", "categoryId": "cat_1749835881685"}], "categories": [{"id": "cat_2", "name": "AI编程", "order": 1}, {"id": "cat_1749835881685", "name": "CSS样式", "order": 2}, {"id": "cat_3", "name": "文章生成", "order": 3}, {"id": "cat_1750365903279", "name": "服务器运维", "order": 4}], "exportTime": "2025-06-24T10:39:28.619Z", "version": "1.0"}