# Enhanced Cursor Rules - AI 编程协作规范

## 🎯 核心理念
构建高效、安全、可维护的 AI 编程协作体系，在保证代码质量的同时确保系统整体稳定性和兼容性。

---

## 📊 优先级分层系统

### P0 级 - 关键规则 (必须遵守)
- **安全性**: 所有代码修改必须考虑安全影响，防止引入安全漏洞
- **数据完整性**: 确保数据一致性，避免数据丢失或损坏
- **系统稳定性**: 不得破坏现有功能，保持系统正常运行
- **同步更新**: 所有相关位置必须同步修改，不可遗漏

### P1 级 - 重要规则 (强烈建议)
- **功能完整性**: 完整实现用户需求，不得中途停止
- **代码质量**: 遵循编码规范，保持代码可读性和可维护性
- **测试覆盖**: 为关键功能添加适当测试
- **错误处理**: 实现完善的错误处理和日志记录

### P2 级 - 建议规则 (最佳实践)
- **性能优化**: 关注代码性能，避免不必要的资源消耗
- **代码风格**: 统一代码格式和命名规范
- **文档完善**: 提供清晰的注释和文档
- **模块化设计**: 遵循模块化设计原则

---

## 🌐 语言规范

### 默认语言策略
- **主要语言**: 始终使用中文简体回复
- **技术术语**: 保持英文原文，如 `function`、`class`、`API` 等
- **代码注释**: 使用中文注释，但保持代码本身的英文命名
- **文档编写**: 中英文混合，确保技术准确性

### 代码注释规范
- **标准**: 使用 **JSDoc** 注释规范
- **覆盖率**: 所有 public 函数、类和复杂逻辑必须添加注释
- **内容**: 包含功能描述、参数说明、返回值、异常情况
- **示例**: 为复杂函数提供使用示例

---

## 🔧 代码质量标准

### 格式规范
- **一致性**: 严格遵循项目现有编码风格
- **可读性**: 使用描述性、明确的变量名
- **常量化**: 将硬编码值替换为命名常量
- **结构化**: 保持良好的代码结构和缩进

### 量化指标
- **函数复杂度**: 单个函数不超过 50 行，圈复杂度 ≤ 10
- **注释覆盖率**: 核心逻辑 100%，一般逻辑 ≥ 80%
- **测试覆盖率**: 新增代码 ≥ 80%，关键路径 100%
- **代码重复率**: ≤ 5%

---

## 🛠️ MCP 工具集成规则

### Context7 使用策略
- **优先场景**: 复杂技术问题、框架文档查询、API 使用说明
- **使用原则**: 根据任务复杂度灵活使用，非强制性
- **最佳实践**: 优先查询官方文档和最佳实践

### Playwright 自动化测试
- **触发条件**: 完成 Web 应用开发后自动执行
- **测试范围**: 核心功能、用户交互流程、边缘情况
- **质量标准**: 自动识别 UI 和功能问题，确保用户体验

---

## 📋 任务执行原则

### 任务管理流程
1. **任务分解**: 创建详细的任务清单和执行计划
2. **进度跟踪**: 实时更新任务状态和完成情况
3. **执行记录**: 创建 `execution-log.md` 记录执行过程和结果
4. **完整执行**: 任务开始后不得中途停止，直至全部完成

### 代码修改工作流
1. **前置调研**: 深入理解现有架构和业务逻辑
2. **思维链推理**: 使用 sequential-thinking 进行逻辑分析
3. **影响评估**: 评估修改对其他模块的潜在影响
4. **分段实施**: 复杂修改分段进行，便于问题定位
5. **验证测试**: 修改后进行功能验证和回归测试

---

## 🛡️ 异常处理机制

### 规则冲突解决策略
```
优先级顺序: 安全性 > 数据完整性 > 功能完整性 > 代码质量 > 性能优化
```

### 技术限制应对方案
- **环境限制**: 提供替代技术方案和实现路径
- **工具限制**: 说明限制原因，提供手动操作指导
- **时间限制**: 采用渐进式实施，优先核心功能

### 紧急情况处理
- **系统故障**: 立即停止操作，保护现有数据
- **安全威胁**: 优先修复安全问题，暂缓其他功能
- **数据风险**: 先备份再操作，确保可回滚

### 错误恢复机制
- **自动回滚**: 检测到严重错误时自动恢复到安全状态
- **增量修复**: 采用小步快跑的方式逐步修复问题
- **状态检查**: 每个关键步骤后验证系统状态

---

## 📈 监控与度量

### 代码质量监控
- **静态分析**: 使用 ESLint、SonarQube 等工具
- **代码审查**: 关键修改必须经过代码审查
- **性能监控**: 监控关键性能指标和资源使用

### 执行效率度量
- **任务完成率**: 按时完成的任务比例
- **缺陷率**: 每千行代码的缺陷数量
- **修复时间**: 从发现问题到修复完成的平均时间

---

## ⚙️ 工具配置建议

### 开发环境配置
```json
{
  "eslint": "^8.0.0",
  "prettier": "^2.0.0", 
  "husky": "^8.0.0",
  "lint-staged": "^13.0.0"
}
```

### 自动化工具链
- **代码格式化**: Prettier + ESLint
- **提交钩子**: Husky + lint-staged
- **测试框架**: Jest/Vitest + Playwright
- **CI/CD**: GitHub Actions/GitLab CI

---

## 👥 团队协作规范

### 沟通协作
- **自动化优先**: 提供自动化解决方案替代手动操作
- **及时反馈**: 发现问题时主动提醒和预警
- **知识共享**: 记录解决方案和最佳实践
- **持续改进**: 定期回顾和优化工作流程

### 变更管理
- **版本控制**: 所有代码变更必须通过版本控制系统
- **变更记录**: 详细记录变更原因、影响范围和测试结果
- **发布管理**: 采用渐进式发布，降低风险

---

## 🔄 持续改进

### 学习机制
- **错误分析**: 从错误中学习，建立问题知识库
- **最佳实践**: 总结和分享成功经验
- **工具更新**: 跟踪新技术和工具，适时升级

### 反馈循环
- **用户反馈**: 收集用户使用反馈，持续优化
- **性能分析**: 定期分析系统性能，识别优化点
- **流程优化**: 基于实际使用情况优化工作流程

---

**最后更新**: 2025-01-15  
**版本**: v2.0  
**维护者**: AI Assistant Team
