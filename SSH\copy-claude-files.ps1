# PowerShell 版本的文件复制脚本
Write-Host "📋 开始复制 Claude Code 包文件..." -ForegroundColor Blue

$tempPackage = "temp-claude-package\package"

if (Test-Path "$tempPackage\cli.js") {
    Write-Host "复制 cli.js..." -ForegroundColor Green
    Copy-Item "$tempPackage\cli.js" "cli.js" -Force
}

if (Test-Path "$tempPackage\yoga.wasm") {
    Write-Host "复制 yoga.wasm..." -ForegroundColor Green
    Copy-Item "$tempPackage\yoga.wasm" "yoga.wasm" -Force
}

if (Test-Path "$tempPackage\vendor") {
    Write-Host "复制 vendor 目录..." -ForegroundColor Green
    Copy-Item "$tempPackage\vendor" -Destination "vendor" -Recurse -Force
}

Write-Host "✅ 文件复制完成" -ForegroundColor Green
